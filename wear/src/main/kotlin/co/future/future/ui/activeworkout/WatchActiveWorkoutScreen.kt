package co.future.future.ui.activeworkout

import android.Manifest
import androidx.compose.foundation.background
import androidx.compose.foundation.focusable
import androidx.compose.foundation.gestures.scrollBy
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.NavigateNext
import androidx.compose.material3.IconButton
import androidx.wear.compose.material.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.hapticfeedback.HapticFeedbackType
import androidx.compose.ui.input.rotary.onRotaryScrollEvent
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalHapticFeedback
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.wear.compose.ui.tooling.preview.WearPreviewDevices
import androidx.wear.compose.ui.tooling.preview.WearPreviewFontScales
import co.future.future.R
import co.future.future.ui.components.AnimatedLinearProgressIndicator
import co.future.future.utils.ModifierExtensions
import co.future.future.utils.ModifierExtensions.fadingEdges
import co.future.future.utils.ModifierExtensions.verticalScrollWithScrollbar
import co.future.futuredesign.FutureTheme
import co.future.futuredesign.PreviewThemeProvider
import co.future.futurekit.models.ActiveWorkoutState
import co.future.futurekit.models.Exercise
import co.future.futurekit.models.ExerciseSet
import co.future.futurekit.models.ExerciseSetType
import com.google.accompanist.permissions.ExperimentalPermissionsApi
import com.google.accompanist.permissions.rememberMultiplePermissionsState
import kotlinx.coroutines.*
import java.util.*

@Composable
fun WatchActiveWorkoutScreen() {
    val viewModel: WatchActiveWorkoutScreenViewModel = hiltViewModel()
    val uiState: WatchActiveWorkoutScreenState by viewModel.uiState.collectAsStateWithLifecycle()

    val context = LocalContext.current

    WatchActiveWorkoutView(
        activeExerciseSet = uiState.currentExerciseSet,
        heartRate = uiState.heartRate,
        workoutState = uiState.activeWorkoutState,
        displayedProgressString = viewModel.getDisplayedProgressString(),
        progress = uiState.currentExerciseSetProgress.toFloat(),
        isTransitioning = uiState.isTransitioning,
        isRecovery = uiState.isRecovery,
        isPaused = uiState.isPaused,
        onPermissionResult = {
            viewModel.onPermissionResult(context)
        },
        onSkipExerciseSet = {
            viewModel.skipExerciseSet()
        }
    )
}

@OptIn(ExperimentalPermissionsApi::class)
@Composable
fun WatchActiveWorkoutView(
    activeExerciseSet: ExerciseSet?,
    heartRate: String,
    workoutState: ActiveWorkoutState,
    displayedProgressString: String?,
    progress: Float,
    isTransitioning: Boolean,
    isRecovery: Boolean,
    isPaused: Boolean,
    onPermissionResult: () -> Unit,
    onSkipExerciseSet: () -> Unit
) {
    val scrollState = rememberScrollState()
    val coroutineScope = rememberCoroutineScope()
    val focusRequester = remember { FocusRequester() }

    val permissions = rememberMultiplePermissionsState(
        permissions = listOf(
            Manifest.permission.BODY_SENSORS
        )
    )

    val isWeightBased = activeExerciseSet?.isWeightBased ?: false
    val exerciseSetType = activeExerciseSet?.type
    val side = activeExerciseSet?.exercise?.side?.shortDisplayName
        ?: activeExerciseSet?.exercise?.side?.displayName ?: ""

    val hapticFeedback = LocalHapticFeedback.current

    val weight = activeExerciseSet?.weight ?: 0.0

    var rotaryScrollPosition by remember {
        mutableFloatStateOf(0f)
    }
    var isRotaryScrolling by remember {
        mutableStateOf(false)
    }

    LaunchedEffect(rotaryScrollPosition) {
        isRotaryScrolling = true
        delay(500)
        isRotaryScrolling = false
    }

    LaunchedEffect(permissions) {
        if (!permissions.allPermissionsGranted) {
            permissions.launchMultiplePermissionRequest()
        } else {
            onPermissionResult()
        }
    }

    LaunchedEffect(activeExerciseSet) {
        hapticFeedback.performHapticFeedback(HapticFeedbackType.LongPress)
    }

    LaunchedEffect(Unit) {
        focusRequester.requestFocus()
    }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .background(FutureTheme.colorScheme.black)
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .verticalScrollWithScrollbar(
                    scrollState,
                    isRotaryScrolling = isRotaryScrolling,
                    scrollbarConfig = ModifierExtensions.ScrollBarConfig(padding = PaddingValues(4.dp)),
                    scope = coroutineScope
                )
                .onRotaryScrollEvent {
                    coroutineScope.launch {
                        rotaryScrollPosition = it.verticalScrollPixels
                        scrollState.scrollBy(rotaryScrollPosition)
                    }
                    true
                }
                .focusRequester(focusRequester)
                .fadingEdges(scrollState)
                .focusable()
                .weight(2f),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            WatchHeartRateView(heartRate = heartRate)
            Text(
                text = activeExerciseSet?.exercise?.name ?: "",
                fontSize = 14.sp,
                color = FutureTheme.colorScheme.white,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(8.dp),
                maxLines = 2,
                overflow = TextOverflow.Ellipsis
            )
            Row(
                modifier = Modifier
                    .height(24.dp)
                    .fillMaxWidth(),
                horizontalArrangement = Arrangement.Center
            ) {
                if (workoutState == ActiveWorkoutState.TRANSITIONING) {
                    Text(
                        text = stringResource(id = R.string.active_workout_get_ready),
                        fontSize = 12.sp,
                        color = FutureTheme.colorScheme.white,
                        modifier = Modifier.padding(bottom = 8.dp)
                    )
                } else if (isWeightBased && weight > 0.0) {
                    Text(
                        text = if (weight.rem(1).equals(0.0)) {
                            "${
                                String.format(
                                    Locale.getDefault(),
                                    "%.0f",
                                    weight
                                )
                            }lbs"
                        } else {
                            "${weight}lbs"
                        },
                        fontSize = 12.sp,
                        color = FutureTheme.colorScheme.white,
                        modifier = Modifier.padding(bottom = 8.dp)
                    )
                } else if (side.isNotEmpty()) {
                    Text(
                        text = side,
                        fontSize = 12.sp,
                        color = FutureTheme.colorScheme.white,
                        modifier = Modifier.padding(bottom = 8.dp)
                    )
                }
            }
        }
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .weight(1f),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            Row(
                modifier = Modifier
                    .wrapContentWidth(align = Alignment.Start)
                    .widthIn(max = 90.dp)
            ) {
                if (workoutState == ActiveWorkoutState.TRANSITIONING) {
                    DurationProgress(
                        displayedProgressString = displayedProgressString,
                        progress = progress,
                        isTransitioning = isTransitioning,
                        isRecovery = isRecovery,
                        isPaused = isPaused
                    )
                } else {
                    // show reps for exercise
                    if (exerciseSetType == ExerciseSetType.REPS) {
                        Text(
                            text = "${activeExerciseSet.reps} reps",
                            fontSize = 15.sp,
                            color = FutureTheme.colorScheme.white
                        )
                    }
                    // show timer for exercise
                    if (exerciseSetType == ExerciseSetType.DURATION) {
                        DurationProgress(
                            displayedProgressString = displayedProgressString,
                            progress = progress,
                            isTransitioning = isTransitioning,
                            isRecovery = isRecovery,
                            isPaused = isPaused
                        )
                    }
                }
            }
            IconButton(onClick = {
                hapticFeedback.performHapticFeedback(HapticFeedbackType.LongPress)
                onSkipExerciseSet()
            }) {
                Icon(
                    imageVector = Icons.Rounded.NavigateNext,
                    contentDescription = stringResource(id = R.string.active_workout_skip_cont_desc),
                    tint = FutureTheme.colorScheme.mint,
                    modifier = Modifier.size(76.dp)
                )
            }
        }
    }
}

@Composable
fun DurationProgress(
    displayedProgressString: String?,
    progress: Float,
    isTransitioning: Boolean,
    isRecovery: Boolean,
    isPaused: Boolean
) {
    Column {
        displayedProgressString?.let {
            Text(
                text = displayedProgressString,
                fontSize = 15.sp,
                color = if (isTransitioning || isRecovery || isPaused) {
                    FutureTheme.colorScheme.gray70
                } else {
                    FutureTheme.colorScheme.white
                }
            )
        }
        AnimatedLinearProgressIndicator(
            progress = progress,
            color = if (isTransitioning || isRecovery || isPaused) {
                FutureTheme.colorScheme.gray70
            } else {
                FutureTheme.colorScheme.mint
            },
            trackColor = FutureTheme.colorScheme.gray90
        )
    }
}

@WearPreviewDevices
@WearPreviewFontScales
@Composable
fun ActiveWorkout_Preview() {
    PreviewThemeProvider {
        val exercise = Exercise(id = "", name = "Alternating Bicep Curls")
        val exerciseSet = ExerciseSet(
            id = "",
            exercise = exercise,
            weight = 120.0,
            type = ExerciseSetType.REPS,
            reps = 20
        )

        WatchActiveWorkoutView(
            activeExerciseSet = exerciseSet,
            heartRate = "120",
            workoutState = ActiveWorkoutState.ACTIVE,
            displayedProgressString = "30",
            progress = 0.5f,
            isTransitioning = false,
            isRecovery = false,
            isPaused = false,
            onPermissionResult = {},
            onSkipExerciseSet = {}
        )
    }
}
