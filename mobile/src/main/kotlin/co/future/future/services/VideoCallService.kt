package co.future.future.services

import android.app.Service
import android.content.Context
import android.content.Intent
import android.media.AudioAttributes
import android.media.MediaPlayer
import android.os.Build
import android.os.VibrationEffect
import android.os.Vibrator
import android.os.VibratorManager
import android.provider.Settings
import androidx.annotation.RequiresApi
import co.daily.CallClient
import co.daily.CallClientListener
import co.daily.model.AvailableDevices
import co.daily.model.CallState
import co.daily.model.MediaDeviceInfo
import co.daily.model.MeetingToken
import co.daily.model.NetworkStats
import co.daily.model.Participant
import co.daily.model.ParticipantCounts
import co.daily.model.ParticipantId
import co.daily.model.ParticipantLeftReason
import co.daily.model.RequestListener
import co.daily.model.livestream.LiveStreamStatus
import co.daily.model.recording.RecordingStatus
import co.daily.model.streaming.StreamId
import co.daily.settings.BitRate
import co.daily.settings.CameraInputSettingsUpdate
import co.daily.settings.CameraPublishingSettingsUpdate
import co.daily.settings.ClientSettingsUpdate
import co.daily.settings.FacingModeUpdate
import co.daily.settings.FrameRate
import co.daily.settings.InputSettings
import co.daily.settings.InputSettingsUpdate
import co.daily.settings.PublishingSettings
import co.daily.settings.PublishingSettingsUpdate
import co.daily.settings.Scale
import co.daily.settings.VideoEncodingSettingsUpdate
import co.daily.settings.VideoEncodingsSettingsUpdate
import co.daily.settings.VideoMaxQualityUpdate
import co.daily.settings.VideoMediaTrackSettingsUpdate
import co.daily.settings.VideoSendSettingsUpdate
import co.daily.settings.subscription.Subscribed
import co.daily.settings.subscription.SubscriptionProfile
import co.daily.settings.subscription.SubscriptionProfileSettings
import co.daily.settings.subscription.SubscriptionProfileSettingsUpdate
import co.daily.settings.subscription.SubscriptionSettings
import co.daily.settings.subscription.SubscriptionSettingsUpdate
import co.daily.settings.subscription.Unsubscribed
import co.daily.settings.subscription.VideoReceiveSettingsUpdate
import co.daily.settings.subscription.VideoSubscriptionSettingsUpdate
import co.daily.settings.subscription.base
import co.future.future.data.videocall.ParticipantDetails
import co.future.future.data.videocall.ParticipantStateTracker
import co.future.future.data.videocall.VideoCallAllParticipantsListener
import co.future.future.data.videocall.VideoCallState
import co.future.future.data.videocall.VideoCallStateListener
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import timber.log.Timber
import java.util.concurrent.CopyOnWriteArrayList

class VideoCallService : Service() {
    // Video call state + listeners
    private var state: VideoCallState = VideoCallState.default()
    private val stateListeners = CopyOnWriteArrayList<VideoCallStateListener>()
    private val allParticipantsListeners = CopyOnWriteArrayList<VideoCallAllParticipantsListener>()
    private val participantStateTrackerListener = ParticipantStateTracker.Listener { participants ->
        updateServiceState { it.copy(remoteParticipantsToShow = participants) }
        updateRemoteVideoSubscriptions()
    }
    private var participantStateTracker = ParticipantStateTracker(participantStateTrackerListener)

    // Daily.co call client + listener
    private var callClient: CallClient? = null
    private val callClientListener = object : CallClientListener {
        override fun onCallStateUpdated(state: CallState) {
            Timber.tag(TAG).i("onCallStateUpdated($state)")

            updateServiceState { it.copy(status = state) }

            if (state == CallState.left) {
                resetParticipants()

                // Workaround for SDK misreporting input state after leaving (internal reference CSDK-1449)
                <EMAIL> {
                    callClient?.setInputsEnabled(camera = camEnabled, microphone = micEnabled)
                }
            }
        }

        override fun onInputsUpdated(inputSettings: InputSettings) {
            Timber.tag(TAG).i("onInputsUpdated($inputSettings)")

            updateServiceState {
                it.copy(
                    localParticipant = it.localParticipant.copy(
                        camEnabled = inputSettings.camera.isEnabled,
                        micEnabled = inputSettings.microphone.isEnabled
                    )
                )
            }
        }

        override fun onPublishingUpdated(publishingSettings: PublishingSettings) {
            Timber.tag(TAG).i("onPublishingUpdated($publishingSettings)")
        }

        override fun onParticipantLeft(participant: Participant, reason: ParticipantLeftReason) {
            Timber.tag(TAG).i("onParticipantLeft($participant, $reason)")

            val details = ParticipantDetails.from(participant)
            allParticipantsListeners.forEach { it.onLeft(details) }

            updateVideoForLocalParticipant(participant)
            participantStateTracker.onParticipantLeft(details)
        }

        override fun onParticipantJoined(participant: Participant) {
            Timber.tag(TAG).i("onParticipantJoined($participant)")

            val details = ParticipantDetails.from(participant)
            allParticipantsListeners.forEach { it.onJoined(details) }

            updateVideoForLocalParticipant(participant)
            participantStateTracker.onParticipantJoined(details)
        }

        override fun onParticipantUpdated(participant: Participant) {
            Timber.tag(TAG).i("onParticipantUpdated($participant)")

            val details = ParticipantDetails.from(participant)
            allParticipantsListeners.forEach { it.onUpdated(details) }

            updateVideoForLocalParticipant(participant)
            participantStateTracker.onParticipantUpdated(details)

            if (participant.info.isLocal && state.localParticipant.username != participant.info.userName) {
                updateServiceState {
                    it.copy(
                        localParticipant = it.localParticipant.copy(
                            username = participant.info.userName ?: ParticipantDetails.defaultUsername
                        )
                    )
                }
            }
        }

        override fun onActiveSpeakerChanged(activeSpeaker: Participant?) {
            Timber.tag(TAG).i("onActiveSpeakerChanged($activeSpeaker)")

            val details = activeSpeaker?.run { ParticipantDetails.from(this) }
            updateServiceState { it.copy(activeSpeaker = details?.id) }
            details?.apply { participantStateTracker.onActiveSpeakerChanged(this) }
        }

        override fun onError(message: String) {
            Timber.tag(TAG).e("onError($message)")
            stateListeners.forEach { it.onError(message) }
        }

        override fun onSubscriptionsUpdated(subscriptions: Map<ParticipantId, SubscriptionSettings>) {
            Timber.tag(TAG).i("onSubscriptionsUpdated($subscriptions)")
        }

        override fun onSubscriptionProfilesUpdated(
            subscriptionProfiles: Map<SubscriptionProfile, SubscriptionProfileSettings>
        ) {
            Timber.tag(TAG).i("onSubscriptionProfilesUpdated($subscriptionProfiles)")
        }

        override fun onAvailableDevicesUpdated(availableDevices: AvailableDevices) {
            Timber.tag(TAG).i("onAvailableDevicesUpdated($availableDevices)")

            updateServiceState {
                it.copy(availableDevices = availableDevices, activeAudioDeviceId = callClient?.audioDevice())
            }
        }

        override fun onAppMessage(message: String, from: ParticipantId) {
            Timber.tag(TAG).i("onAppMessage($message, $from)")
        }

        override fun onParticipantCountsUpdated(newParticipantCounts: ParticipantCounts) {
            Timber.tag(TAG).i("onParticipantCountsUpdated($newParticipantCounts)")
            updateServiceState { it.copy(participantCount = newParticipantCounts.present) }
        }

        override fun onNetworkStatsUpdated(newNetworkStatistics: NetworkStats) {
            Timber.tag(TAG).i("onNetworkStatsUpdated($newNetworkStatistics)")
        }

        override fun onRecordingStarted(status: RecordingStatus) {
            Timber.tag(TAG).i("onRecordingStarted($status)")
        }

        override fun onRecordingStopped(streamId: StreamId) {
            Timber.tag(TAG).i("onRecordingStopped($streamId)")
        }

        override fun onRecordingError(streamId: StreamId, message: String) {
            Timber.tag(TAG).e("onRecordingError($streamId, $message)")
            stateListeners.forEach { it.onError("Recording error: $message") }
        }

        override fun onLiveStreamStarted(status: LiveStreamStatus) {
            Timber.tag(TAG).i("onLiveStreamStarted($status)")
        }

        override fun onLiveStreamStopped(streamId: StreamId) {
            Timber.tag(TAG).i("onLiveStreamStopped($streamId)")
        }

        override fun onLiveStreamError(streamId: StreamId, message: String) {
            Timber.tag(TAG).e("onLiveStreamError($streamId, $message)")
            stateListeners.forEach { it.onError("Live stream error: $message") }
        }

        override fun onLiveStreamWarning(streamId: StreamId, message: String) {
            Timber.tag(TAG).w("onLiveStreamWarning($streamId, $message)")
            stateListeners.forEach { it.onError("Live stream warning: $message") }
        }
    }

    // Settings & preferences
    private val profileActiveCamera = SubscriptionProfile("activeCamera")
    private val profileActiveScreenShare = SubscriptionProfile("activeScreenShare")
    private var cameraDirection = FacingModeUpdate.user

    override fun onCreate() {
        super.onCreate()
        Timber.tag(TAG).i("VideoCallService onCreate")

        try {
            // Set up call client
            callClient = CallClient(appContext = baseContext).apply {
                addListener(callClientListener)
                setupParticipantSubscriptionProfiles(this)
                setInputsEnabled(camera = false, microphone = false)
                updateVideoForLocalParticipant(participants().local)
            }
        } catch (error: Exception) {
            Timber.tag(TAG).e(error, "Got exception while creating CallClient")
            stateListeners.forEach { it.onError("Failed to initialize call client") }
        }
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Timber.tag(TAG).i("VideoCallService onStartCommand: intent=$intent, flags=$flags, startId=$startId")
        if (intent?.action == ACTION_LEAVE) {
            callClient?.leave()
            stopSelf()
        }
        return START_STICKY
    }

    override fun onBind(intent: Intent?): Binder {
        Timber.tag(TAG).i("onBind")
        return Binder()
    }

    override fun onUnbind(intent: Intent?): Boolean {
        Timber.tag(TAG).i("onUnbind")
        stopSelf()
        return false
    }

    override fun onDestroy() {
        super.onDestroy()
        Timber.tag(TAG).i("VideoCallService onDestroy")

        stopRingtone()

        callClient?.leave()
        callClient?.release()
        callClient = null

        // Ensure we clean up any lingering state
        state = VideoCallState.default()
    }

    private fun updateServiceState(stateUpdate: (VideoCallState) -> VideoCallState) {
        val newState = stateUpdate(state)
        state = newState
        stateListeners.forEach { it.onStateChanged(newState) }
    }

    private fun updateRemoteVideoSubscriptions() {
        callClient?.apply {
            updateSubscriptions(
                // Subscribe to the currently displayed participant
                forParticipants = state.remoteParticipantsToShow.keys.map { it.id }.associateWith {
                    SubscriptionSettingsUpdate(profile = profileActiveCamera)
                },
                // Unsubscribe from remote participants not currently displayed
                forParticipantsWithProfiles = mapOf(
                    profileActiveCamera to SubscriptionSettingsUpdate(profile = SubscriptionProfile.base),
                    profileActiveScreenShare to SubscriptionSettingsUpdate(profile = SubscriptionProfile.base)
                )
            )
        }
    }

    private fun resetParticipants() {
        participantStateTracker = ParticipantStateTracker(participantStateTrackerListener)

        // We don't reset participantCounts here because the SDK doesn't
        // give us another callback if the value in a new call is the same
        // as the old call.
        updateServiceState { it.copy(remoteParticipantsToShow = emptyMap()) }
    }

    private fun setupParticipantSubscriptionProfiles(callClient: CallClient) {
        callClient.updateSubscriptionProfiles(
            mapOf(
                profileActiveCamera to
                    SubscriptionProfileSettingsUpdate(
                        camera = VideoSubscriptionSettingsUpdate(
                            subscriptionState = Subscribed(),
                            receiveSettings = VideoReceiveSettingsUpdate(
                                maxQuality = VideoMaxQualityUpdate.high
                            )
                        ),
                        screenVideo = VideoSubscriptionSettingsUpdate(
                            subscriptionState = Unsubscribed()
                        )
                    ),
                profileActiveScreenShare to
                    SubscriptionProfileSettingsUpdate(
                        camera = VideoSubscriptionSettingsUpdate(
                            subscriptionState = Unsubscribed()
                        ),
                        screenVideo = VideoSubscriptionSettingsUpdate(
                            subscriptionState = Subscribed(),
                            receiveSettings = VideoReceiveSettingsUpdate(
                                maxQuality = VideoMaxQualityUpdate.high
                            )
                        )
                    ),
                SubscriptionProfile.base to
                    SubscriptionProfileSettingsUpdate(
                        camera = VideoSubscriptionSettingsUpdate(
                            subscriptionState = Unsubscribed()
                        ),
                        screenVideo = VideoSubscriptionSettingsUpdate(
                            subscriptionState = Unsubscribed()
                        )
                    )
            )
        )
    }

    private fun updateVideoForLocalParticipant(participant: Participant) {
        if (participant.info.isLocal) {
            updateServiceState {
                it.copy(localParticipant = it.localParticipant.copy(videoTrack = participant.media?.camera?.track))
            }
        }
    }

    inner class Binder : android.os.Binder() {
        val state: VideoCallState
            get() = <EMAIL>

        fun addListener(listener: VideoCallStateListener) {
            stateListeners.add(listener)
            listener.onStateChanged(state)
        }

        fun removeListener(listener: VideoCallStateListener) {
            stateListeners.remove(listener)
        }

        fun join(url: String, token: MeetingToken?) {
            if (state.status == CallState.joining) {
                Timber.tag(TAG).w("User is already joining, ignoring...")
                return
            }

            Timber.tag(TAG).i("Updating call service state to joining")
            updateServiceState { it.copy(status = CallState.joining) }

            Timber.tag(TAG).i("Creating client settings")
            val clientSettings = ClientSettingsUpdate(
                publishingSettings = PublishingSettingsUpdate(
                    camera = CameraPublishingSettingsUpdate(
                        sendSettings = VideoSendSettingsUpdate(
                            encodings = VideoEncodingsSettingsUpdate(
                                settings = mapOf(
                                    VideoMaxQualityUpdate.low to
                                        VideoEncodingSettingsUpdate(
                                            maxBitrate = BitRate(80000),
                                            maxFramerate = FrameRate(10),
                                            scaleResolutionDownBy = Scale(4F)
                                        ),
                                    VideoMaxQualityUpdate.medium to
                                        VideoEncodingSettingsUpdate(
                                            maxBitrate = BitRate(680000),
                                            maxFramerate = FrameRate(30),
                                            scaleResolutionDownBy = Scale(1F)
                                        )
                                )
                            )
                        )
                    )
                )
            )

            Timber.tag(TAG).i("Joining call client...")

            callClient?.join(url, token, clientSettings) {
                Timber.tag(TAG).i("Received join call response: $it")
                it.error?.let { error ->
                    Timber.tag(TAG).e("Got error while joining call: ${error.msg}")
                    stateListeners.forEach { listener -> listener.onError("Failed to join call: ${error.msg}") }
                }
                it.success?.let { data ->
                    Timber.tag(TAG).i("Successfully joined call with session id: ${data.meetingSession.id}")
                }
            }
            Timber.tag(TAG).i("Sent join call signal to call client with url: $url | token: $token")
        }

        fun leave(listener: RequestListener? = null) {
            updateServiceState { it.copy(status = CallState.leaving) }
            callClient?.leave(listener)

            stopSelf()
        }

        fun flipCameraDirection(listener: RequestListener? = null) {
            cameraDirection = when (cameraDirection) {
                FacingModeUpdate.user -> FacingModeUpdate.environment
                FacingModeUpdate.environment -> FacingModeUpdate.user
            }

            callClient?.updateInputs(
                inputSettings = InputSettingsUpdate(
                    camera = CameraInputSettingsUpdate(
                        settings = VideoMediaTrackSettingsUpdate(facingMode = cameraDirection)
                    )
                ),
                listener = listener
            )
            updateServiceState { it.copy(cameraDirection = cameraDirection) }
        }

        fun toggleMicInput(enabled: Boolean, listener: RequestListener? = null) {
            Timber.tag(TAG).i("toggleMicInput $enabled")
            callClient?.setInputsEnabled(microphone = enabled, listener = listener)
        }

        fun toggleCamInput(enabled: Boolean, listener: RequestListener? = null) {
            Timber.tag(TAG).i("toggleCamInput $enabled")
            callClient?.setInputsEnabled(camera = enabled, listener = listener)
        }

        fun setAudioDevice(device: MediaDeviceInfo) {
            Timber.tag(TAG).i("Setting audio device to $device")
            if (device.deviceId != state.activeAudioDeviceId) {
                callClient?.setAudioDevice(device.deviceId)
                updateServiceState { it.copy(activeAudioDeviceId = device.deviceId) }
            }
        }
    }

    companion object {
        private const val TAG = "VideoCallService"
        private const val ACTION_LEAVE = "action_leave"
        const val DEFAULT_RINGING_DURATION = 30000L

        private var ringtonePlayer: MediaPlayer? = null
        private var ringingTimerJob: Job? = null

        @RequiresApi(Build.VERSION_CODES.S)
        private var vibratorManager: VibratorManager? = null
        private var deprecatedVibrator: Vibrator? = null

        fun leaveIntent(context: Context): Intent =
            Intent(context, VideoCallService::class.java).apply { action = ACTION_LEAVE }

        @Suppress("DEPRECATION")
        fun startRingtone(context: Context) {
            // Stop any previously playing ringtone
            stopRingtone()

            Timber.tag(TAG).i("Starting ringtone + vibration")

            ringtonePlayer = MediaPlayer.create(context, Settings.System.DEFAULT_RINGTONE_URI)
            ringtonePlayer?.setAudioAttributes(
                AudioAttributes.Builder().run {
                    setUsage(AudioAttributes.USAGE_NOTIFICATION_RINGTONE)
                    setContentType(AudioAttributes.CONTENT_TYPE_SONIFICATION)
                }.build()
            )
            ringtonePlayer?.start()

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                Timber.tag(TAG).i("Using default vibrator from VIBRATOR_MANAGER_SERVICE for SDK >= 31")
                vibratorManager = context.getSystemService(Context.VIBRATOR_MANAGER_SERVICE) as VibratorManager
                vibratorManager?.defaultVibrator?.vibrate(
                    VibrationEffect.createOneShot(DEFAULT_RINGING_DURATION, VibrationEffect.DEFAULT_AMPLITUDE)
                )
            } else {
                Timber.tag(TAG).i("Directly using vibrator service for SDK < 31")
                deprecatedVibrator = context.getSystemService(Context.VIBRATOR_SERVICE) as Vibrator
                deprecatedVibrator?.vibrate(DEFAULT_RINGING_DURATION)
            }

            ringingTimerJob = CoroutineScope(Dispatchers.Main).launch {
                delay(DEFAULT_RINGING_DURATION)

                Timber.tag(TAG).i("Stopping ringtone + vibration player after 30 seconds")
                stopRingtone()
            }
        }

        fun stopRingtone() {
            Timber.tag(TAG).i("Stopping ringtone + vibration")

            try {
                ringtonePlayer?.stop()
                ringtonePlayer?.release()
                ringtonePlayer = null

                ringingTimerJob?.cancel()
                ringingTimerJob = null

                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                    vibratorManager?.defaultVibrator?.cancel()
                    vibratorManager?.cancel()
                    vibratorManager = null
                } else {
                    deprecatedVibrator?.cancel()
                    deprecatedVibrator = null
                }
            } catch (e: Exception) {
                Timber.tag(TAG).e(e, "Got exception while stopping ringtone")
            }
        }
    }
}
