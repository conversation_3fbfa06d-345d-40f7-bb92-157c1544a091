package co.future.future.ui.videocall.components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import co.future.futuredesign.FutureColor
import co.future.futuredesign.Regular

@Composable
fun VideoCallActionButton(
    icon: ImageVector,
    iconSize: Dp = 48.dp,
    title: String? = null,
    isActive: Boolean = true,
    contentColor: Color = FutureColor.Black,
    backgroundColor: Color = FutureColor.White,
    enabled: Boolean = true,
    onClick: () -> Unit = {}
) {
    Column(horizontalAlignment = Alignment.CenterHorizontally) {
        IconButton(
            modifier = Modifier.size(iconSize),
            enabled = enabled,
            onClick = onClick,
            content = {
                Box(
                    modifier = Modifier
                        .size(iconSize)
                        .background(
                            color = if (isActive) backgroundColor else FutureColor.Black.copy(alpha = 0.2f),
                            shape = RoundedCornerShape(iconSize.div(2))
                        ),
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        modifier = Modifier.size(iconSize.div(1.66f)),
                        imageVector = icon,
                        contentDescription = null,
                        tint = if (isActive) contentColor else FutureColor.White
                    )
                }
            }
        )

        if (title != null) {
            Text(
                modifier = Modifier.padding(top = 18.dp),
                text = title,
                fontSize = 16.sp,
                fontWeight = FontWeight.Regular,
                color = FutureColor.White.copy(alpha = 0.8f),
                textAlign = TextAlign.Center
            )
        }
    }
}
