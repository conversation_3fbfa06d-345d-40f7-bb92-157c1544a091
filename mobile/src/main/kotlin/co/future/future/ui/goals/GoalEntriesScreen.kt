package co.future.future.ui.goals

import androidx.activity.compose.BackHandler
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.TrendingUp
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawWithCache
import androidx.compose.ui.graphics.BlendMode
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.TextUnitType
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.em
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import co.future.future.R
import co.future.future.ui.dialogs.GoalUpdateMeasurementRequestDialog
import co.future.futuredesign.FutureTheme
import co.future.futurekit.extensions.capitalizedFirstLetter
import co.future.futurekit.models.GoalEntry
import co.future.futurekit.models.GoalType
import co.future.futurekit.utils.DateUtils
import coil.compose.AsyncImage
import coil.request.ImageRequest
import timber.log.Timber
import java.time.ZonedDateTime

private const val TAG = "GoalEntriesScreen"
private const val IMAGE_SIZE = 96

@OptIn(ExperimentalFoundationApi::class)
@Composable
fun GoalEntriesScreen() {
    val viewModel: GoalEntriesScreenViewModel = hiltViewModel()
    val uiState: GoalEntriesScreenViewScreenState by viewModel.uiState.collectAsStateWithLifecycle()

    val lazyListState = rememberLazyListState()
    val isMeasurement = uiState.goal?.type == GoalType.WEIGHT || uiState.goal?.type == GoalType.BODY_MEASUREMENT
    val firstEntry = uiState.entries.firstOrNull()

    // Explicitly pop back up to parent
    BackHandler {
        viewModel.popBackStack()
    }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .fillMaxHeight(0.92f)
            .navigationBarsPadding()
            .background(
                color = FutureTheme.colorScheme.contentBackground,
                shape = RoundedCornerShape(topStart = 10.dp, topEnd = 10.dp)
            )
            .padding(top = 18.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // Action header
        Box(
            modifier = Modifier.fillMaxWidth(),
        ) {
            Text(
                modifier = Modifier.align(Alignment.Center),
                text = uiState.title
                    ?: stringResource(id = R.string.goal_entries_title).uppercase(),
                color = FutureTheme.colorScheme.gray20,
                fontSize = 12.sp,
                letterSpacing = TextUnit(1f, TextUnitType.Sp),
            )
            TextButton(
                modifier = Modifier.align(Alignment.CenterEnd),
                onClick = viewModel::closeScreen
            ) {
                Text(
                    text = stringResource(id = R.string.goal_entries_done_button),
                    color = FutureTheme.colorScheme.textBlack,
                    fontSize = 16.sp
                )
            }
        }

        // Entry header
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 24.dp, vertical = 16.dp),
            horizontalAlignment = Alignment.Start,
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Text(
                text = uiState.goal?.name ?: "",
                color = FutureTheme.colorScheme.textBlack,
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
            )
            uiState.checkInFrequency?.let {
                Text(
                    text = it,
                    color = FutureTheme.colorScheme.textGray,
                    fontSize = 14.sp,
                )
            }
        }

        // For measurement goals, show a preview of the last entry
        if (isMeasurement && firstEntry != null) {
            Spacer(modifier = Modifier.height(24.dp))

            Row(verticalAlignment = Alignment.CenterVertically) {
                val brush = Brush.horizontalGradient(
                    colors = listOf(
                        FutureTheme.colorScheme.mint,
                        FutureTheme.colorScheme.lime
                    )
                )

                Icon(
                    imageVector = Icons.AutoMirrored.Filled.TrendingUp,
                    contentDescription = null,
                    tint = FutureTheme.colorScheme.mint,
                    modifier = Modifier
                        .size(72.dp)
                        .graphicsLayer(alpha = 0.99f)
                        .drawWithCache {
                            onDrawWithContent {
                                drawContent()
                                drawRect(brush, blendMode = BlendMode.SrcAtop)
                            }
                        }
                )

                Spacer(modifier = Modifier.width(24.dp))

                Column {
                    // Recorded date
                    Text(
                        text = DateUtils.shortMonthDayYearFormatter.format(
                            firstEntry.recordedDate ?: firstEntry.recordedAt ?: ZonedDateTime.now()
                        ).uppercase(),
                        fontSize = 12.sp,
                        color = FutureTheme.colorScheme.textGray,
                        fontWeight = FontWeight.Medium,
                        letterSpacing = 0.1.em,
                        textAlign = TextAlign.Start
                    )

                    Spacer(modifier = Modifier.height(2.dp))

                    // Goal entry value
                    Text(
                        text = "${firstEntry.currentValue ?: "-"} ${uiState.goal?.unit ?: ""}",
                        fontSize = 36.sp,
                        color = FutureTheme.colorScheme.textBlack,
                        fontWeight = FontWeight.Medium,
                        letterSpacing = -(0.04).em,
                        textAlign = TextAlign.Start,
                        maxLines = 1
                    )
                }
            }

            Spacer(modifier = Modifier.height(24.dp))
        }

        // Main entries list
        LazyColumn(
            modifier = Modifier.fillMaxWidth().weight(1f),
            state = lazyListState
        ) {
            stickyHeader {
                HorizontalDivider(
                    color = FutureTheme.colorScheme.divider,
                    thickness = Dp.Hairline,
                )

                // Header for weight / measurement entries
                if (isMeasurement) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = 24.dp, vertical = 6.dp)
                    ) {
                        Text(
                            modifier = Modifier.weight(0.5f),
                            text = stringResource(id = R.string.goal_entries_date_header),
                            fontSize = 14.sp,
                            color = FutureTheme.colorScheme.textGray,
                            fontWeight = FontWeight.Medium
                        )
                        Text(
                            modifier = Modifier.weight(0.5f),
                            text = uiState.goal?.entries?.firstOrNull()?.entryAttributes?.firstOrNull()?.type?.capitalizedFirstLetter() ?: "Value",
                            fontSize = 14.sp,
                            color = FutureTheme.colorScheme.textGray,
                            fontWeight = FontWeight.Medium
                        )
                    }
                    HorizontalDivider(
                        color = FutureTheme.colorScheme.divider,
                        thickness = Dp.Hairline,
                    )
                }

                Spacer(modifier = Modifier.height(8.dp))
            }

            items(items = uiState.entries) { entry ->
                when (uiState.goal?.type) {
                    GoalType.PROGRESS_PHOTOS -> {
                        ProgressPhotoEntryItem(entry)
                    }
                    GoalType.WEIGHT, GoalType.BODY_MEASUREMENT -> {
                        MeasurementEntryItem(entry, uiState.goal?.unit)
                    }
                    else -> {
                        // Unsupported entry type
                    }
                }
            }
        }

        HorizontalDivider(
            color = FutureTheme.colorScheme.divider,
            thickness = Dp.Hairline,
        )

        // Action button
        TextButton(
            modifier = Modifier.fillMaxWidth().height(64.dp),
            onClick = viewModel::addEntry,
            colors = ButtonDefaults.buttonColors(
                containerColor = FutureTheme.colorScheme.transparent,
                contentColor = FutureTheme.colorScheme.maize
            )
        ) {
            Text(
                text = uiState.addEntryButtonText,
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium
            )
        }
    }

    // Goal update dialog
    if (uiState.displayGoalUpdateRequest) {
        uiState.goal?.let {
            GoalUpdateMeasurementRequestDialog(
                goal = it,
                onClickUpdate = { value ->
                    viewModel.updateGoalMeasurementValue(goal = it, value = value)
                },
                onClickDismiss = {
                    viewModel.dismissGoalUpdateRequestDialog()
                }
            )
        }
    }
}

@Composable
fun ProgressPhotoEntryItem(entry: GoalEntry) {
    val context = LocalContext.current

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 24.dp, vertical = 8.dp)
    ) {
        // Date recorded
        entry.recordedAt?.let {
            Text(
                text = DateUtils.shortMonthDayYearFormatter.format(it),
                fontSize = 14.sp,
                color = FutureTheme.colorScheme.textGray,
                fontWeight = FontWeight.Medium
            )
        }

        Spacer(modifier = Modifier.height(8.dp))

        Row(
            modifier = Modifier
                .fillMaxWidth()
                .height(IMAGE_SIZE.dp)
                .horizontalScroll(rememberScrollState()),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            entry.entryAttributes?.forEach { attributes ->
                AsyncImage(
                    modifier = Modifier.size(IMAGE_SIZE.dp),
                    model = ImageRequest.Builder(context)
                        .data(attributes.imageUrl)
                        .crossfade(true)
                        .build(),
                    contentDescription = stringResource(id = R.string.goal_entries_progress_photo_image_content_dec),
                    contentScale = ContentScale.Crop,
                    onError = {
                        Timber.tag(TAG).e(it.result.throwable, "Failed to load preview image")
                    },
                    onSuccess = {}
                )
            }
        }
    }
}

@Composable
fun MeasurementEntryItem(entry: GoalEntry, unit: String? = null) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 24.dp, vertical = 4.dp)
    ) {
        // Date recorded
        entry.recordedAt?.let {
            Text(
                modifier = Modifier.weight(1f),
                text = DateUtils.shortMonthDayYearFormatter.format(it),
                fontSize = 14.sp,
                color = FutureTheme.colorScheme.textBlack,
            )
        }

        // Measurement value
        Text(
            modifier = Modifier.weight(1f),
            text = "${entry.currentValue}" + if (unit != null) " $unit" else "",
            fontSize = 14.sp,
            color = FutureTheme.colorScheme.textBlack,
        )
    }
}
