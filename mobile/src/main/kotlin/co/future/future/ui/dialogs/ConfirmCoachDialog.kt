package co.future.future.ui.dialogs

import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import co.future.future.R
import co.future.future.ui.components.TrainerIcon
import co.future.futuredesign.FutureTheme

@Composable
fun ConfirmCoachDialog(
    trainerFirstName: String,
    trainerImageUrl: String? = null,
    onConfirm: () -> Unit = {},
    onDismiss: () -> Unit = {}
) {
    val trainWithText = stringResource(R.string.coach_selection_confirm_coach_dialog_train_with_text, trainerFirstName)

    AlertDialog(
        onDismissRequest = { onDismiss() },
        // Material 3 displays the confirm button first and Future mocks displays dismiss button first
        // resulting in the confirmButton property being used as a dismiss button.
        confirmButton = {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.Center
            ) {
                Button(
                    onClick = { onDismiss() },
                    shape = RoundedCornerShape(2.dp),
                    modifier = Modifier.fillMaxWidth(),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = FutureTheme.colorScheme.white,
                        contentColor = FutureTheme.colorScheme.textBlack
                    )
                ) {
                    Text(
                        text = stringResource(
                            id = R.string.coach_selection_confirm_coach_dialog_back_button_text
                        ).uppercase(),
                        textAlign = TextAlign.Center,
                        fontSize = 12.sp
                    )
                }
            }
        },
        // Material 3 displays the dismiss button second and Future mocks displays confirm button second
        // resulting in the dismissButton property being used as a confirm button.
        dismissButton = {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.Center
            ) {
                Button(
                    modifier = Modifier
                        .fillMaxWidth()
                        .testTag("ConfirmCoachTrainWithButton"),
                    onClick = { onConfirm() },
                    shape = RoundedCornerShape(2.dp),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = FutureTheme.colorScheme.almostBlack,
                        contentColor = FutureTheme.colorScheme.gray95
                    )
                ) {
                    Text(
                        text = trainWithText.uppercase(),
                        textAlign = TextAlign.Center,
                        fontSize = 12.sp
                    )
                }
            }
        },
        icon = {
            TrainerIcon(
                modifier = Modifier
                    .border(
                        width = 1.dp,
                        color = FutureTheme.colorScheme.gray90,
                        shape = CircleShape
                    ),
                trainerImageUrl = trainerImageUrl,
                size = 96.dp
            )
        },
        title = {
            Text(
                text = "$trainWithText?",
                modifier = Modifier.fillMaxWidth(),
                textAlign = TextAlign.Center,
                fontSize = 16.sp,
                fontWeight = FontWeight.Bold,
                color = FutureTheme.colorScheme.textBlack
            )
        },
        text = {
            Text(
                text = stringResource(id = R.string.coach_selection_confirm_coach_dialog_body_text),
                modifier = Modifier.fillMaxWidth(),
                textAlign = TextAlign.Center,
                fontSize = 14.sp,
                color = FutureTheme.colorScheme.textGray
            )
        },
        containerColor = FutureTheme.colorScheme.gray95,
        shape = RoundedCornerShape(2.dp)
    )
}
