package co.future.future.ui.wrapped.how

import android.icu.text.NumberFormat
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.systemBarsPadding
import androidx.compose.foundation.layout.width
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.em
import co.future.future.extensions.scaledSp
import co.future.future.providers.previewtheme.PreviewThemeProvider
import co.future.future.ui.wrapped.WrappedPage
import co.future.future.ui.wrapped.components.WrappedStatText
import co.future.future.ui.wrapped.components.WrappedTitleComponent
import co.future.future.ui.wrapped.components.WrappedTitleText
import co.future.futuredesign.FutureColor
import co.future.futuredesign.Regular
import java.util.*

@Composable
fun WrappedHowMileageView(
    page: WrappedPage.HowMileage,
    contentColor: Color
) {
    val longestMonthDistance = page.milesByMonth.maxByOrNull { it.second }?.second ?: 10.0

    val numberFormatter = NumberFormat
        .getInstance(Locale.getDefault())
        .apply {
            maximumFractionDigits = 1
            minimumFractionDigits = 0
        }

    // Main content
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .systemBarsPadding()
            .padding(start = 18.dp, top = 96.dp, end = 18.dp, bottom = 48.dp),
        verticalArrangement = Arrangement.spacedBy(24.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .weight(1f),
            verticalArrangement = Arrangement.spacedBy(14.dp)
        ) {
            page.milesByMonth.forEach {
                val graphColor = if (it.second == longestMonthDistance) {
                    FutureColor.Yellow
                } else {
                    FutureColor.Gray80
                }
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .weight(1f),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        modifier = Modifier.width(36.dp),
                        text = it.first,
                        fontSize = 16.scaledSp(),
                        fontWeight = FontWeight.Regular,
                        color = FutureColor.Gray50,
                        letterSpacing = -(0.02).em,
                        lineHeight = 16.scaledSp(),
                        textAlign = TextAlign.End
                    )

                    Spacer(modifier = Modifier.width(12.dp))

                    Box(
                        modifier = Modifier
                            .weight(1f)
                            .fillMaxHeight(),
                    ) {
                        Box(
                            modifier = Modifier
                                .background(graphColor)
                                .fillMaxHeight()
                                .fillMaxWidth((it.second / longestMonthDistance).toFloat())
                        )
                    }

                    Spacer(modifier = Modifier.width(12.dp))

                    Text(
                        modifier = Modifier.width(72.dp),
                        text = "${numberFormatter.format(it.second)} mi",
                        fontSize = 16.scaledSp(),
                        fontWeight = FontWeight.Regular,
                        color = FutureColor.Gray50,
                        letterSpacing = -(0.02).em,
                        lineHeight = 16.scaledSp(),
                        textAlign = TextAlign.Start
                    )
                }
            }
        }

        WrappedTitleText(
            components = listOf(
                WrappedTitleComponent(text = "Going the extra mile.")
            ),
            color = contentColor
        )

        Row(modifier = Modifier.fillMaxWidth()) {
            WrappedStatText(
                modifier = Modifier.weight(1.5f),
                label = "TOTAL MILEAGE",
                value = "${numberFormatter.format(page.totalMileage)} Miles",
                valueSubtitle = "Avg. ${numberFormatter.format(page.averageDistance)}/mo",
                color = contentColor
            )
            WrappedStatText(
                modifier = Modifier.weight(1.5f),
                label = "LONGEST DISTANCE",
                value = "${numberFormatter.format(page.longestDistance)} Miles",
                valueSubtitle = page.longestDistanceDateString,
                color = contentColor
            )
        }
    }
}

@Preview(name = "Base", showBackground = true)
@Composable
fun WrappedHowMileageView_Preview() {
    PreviewThemeProvider {
        WrappedHowMileageView(
            page = WrappedPage.HowMileage(
                milesByMonth = listOf(
                    "Jan" to 22.0,
                    "Feb" to 43.0,
                    "Mar" to 19.0,
                    "Apr" to 10.0,
                    "May" to 8.4,
                    "Jun" to 0.0,
                    "Jul" to 4.4,
                    "Aug" to 45.0,
                    "Sep" to 61.0,
                    "Oct" to 63.0,
                    "Nov" to 41.0,
                    "Dec" to 4.0
                ),
                totalMileage = 345.1,
                averageDistance = 23.5,
                longestDistance = 4.2,
                longestDistanceDateString = "Oct 2"
            ),
            contentColor = Color.Black
        )
    }
}
