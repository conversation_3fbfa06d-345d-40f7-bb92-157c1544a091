package co.future.future.ui.guidedworkout.overview

import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Text
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalLifecycleOwner
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.media3.common.MediaItem
import androidx.media3.common.Player
import androidx.media3.exoplayer.ExoPlayer
import co.future.future.ui.dialogs.DistanceAdjustmentDialog
import co.future.future.ui.dialogs.RepAdjustmentDialog
import co.future.future.ui.dialogs.WeightAdjustmentDialog
import co.future.future.ui.dialogs.WorkoutAdjustmentDialogType
import co.future.future.ui.guidedworkout.exercisecontrol.GuidedWorkoutExerciseControlButtonType
import co.future.future.ui.guidedworkout.modifyexercise.ModifyExerciseBottomSheet
import co.future.futuredesign.FutureTheme
import co.future.futuredesign.Regular
import co.future.futurekit.extensions.naturalMinuteDurationString
import co.future.futurekit.models.ActiveWorkoutState
import co.future.futurekit.models.ExerciseSet
import co.future.futurekit.models.ExerciseSetCompletionState
import co.future.futurekit.models.ExerciseSetFlagTag
import co.future.futurekit.models.ExerciseSetFlagTagType
import co.future.futurekit.models.WorkoutAudioType
import co.future.futurekit.models.audiosOfType
import kotlinx.coroutines.launch
import timber.log.Timber

private const val TAG = "GuidedWorkoutOverviewView"

@OptIn(ExperimentalFoundationApi::class, ExperimentalMaterial3Api::class)
@Composable
fun GuidedWorkoutOverviewView(
    onDismissWorkoutOverview: () -> Unit,
    onExerciseFlagged: () -> Unit,
    onExerciseSwapped: () -> Unit,
    onExerciseHistoryClicked: () -> Unit
) {
    val context = LocalContext.current
    val lifecycleOwner = LocalLifecycleOwner.current
    val scope = rememberCoroutineScope()
    val lazyListState = rememberLazyListState()

    val modifyExerciseSheetState = rememberModalBottomSheetState(skipPartiallyExpanded = true)

    // ViewModel
    val viewModel: GuidedWorkoutOverviewViewModel = hiltViewModel()
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()

    // Video player for playing exercise set videos
    val videoPlayer = remember(context) {
        ExoPlayer.Builder(context)
            .setMediaSourceFactory(viewModel.mediaSourceFactory)
            .build()
            .apply {
                repeatMode = Player.REPEAT_MODE_ALL
            }
    }

    // Keep track of expanded exercise set row
    var expandedExerciseSet: ExerciseSet? by remember { mutableStateOf(null) }

    // Scroll to current exercise set
    LaunchedEffect(uiState.workout) {
        val workout = uiState.workout ?: return@LaunchedEffect
        val currentExerciseSetId = uiState.currentExerciseSet?.id ?: return@LaunchedEffect
        val index =
            workout.exerciseSetIndexForExerciseSetId(currentExerciseSetId) ?: return@LaunchedEffect

        try {
            lazyListState.scrollToItem(index = index)
        } catch (e: Throwable) {
            Timber.tag(TAG).e(e, "Error scrolling to today's workout at $index")
        }
    }

    LaunchedEffect(expandedExerciseSet?.id) {
        expandedExerciseSet?.videoUrl?.let { videoUrl ->
            val mediaItem = MediaItem.Builder()
                .setUri(videoUrl)
                .build()
            videoPlayer.setMediaItem(mediaItem)
            videoPlayer.prepare()
            videoPlayer.play()
        } ?: run {
            videoPlayer.stop()
        }
    }

    LaunchedEffect(uiState.showModifyExerciseBottomSheet) {
        if (uiState.showModifyExerciseBottomSheet) {
            modifyExerciseSheetState.show()
        } else {
            modifyExerciseSheetState.hide()
        }
    }

    DisposableEffect(videoPlayer) {
        val lifecycleObserver = LifecycleEventObserver { _, event ->
            when (event) {
                Lifecycle.Event.ON_START -> videoPlayer.playWhenReady = true
                Lifecycle.Event.ON_STOP -> videoPlayer.playWhenReady = false
                else -> {}
            }
        }
        lifecycleOwner.lifecycle.addObserver(lifecycleObserver)
        onDispose {
            lifecycleOwner.lifecycle.removeObserver(lifecycleObserver)
            videoPlayer.stop()
            videoPlayer.release()
        }
    }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .navigationBarsPadding()
            .background(
                color = FutureTheme.colorScheme.white,
                shape = RoundedCornerShape(topStart = 10.dp, topEnd = 10.dp)
            )
            .padding(top = 6.dp)
            .testTag("GuidedWorkoutOverviewView")
    ) {
        uiState.workout?.let { workout ->
            LazyColumn(state = lazyListState) {
                workout.audios.audiosOfType(WorkoutAudioType.INTRO).firstOrNull()
                    ?.let { workoutAudio ->
                        item(key = workoutAudio.id) {
                            GuidedWorkoutOverviewIntroAudioRow(
                                trainerImageUrl = uiState.currentTrainerImageUrl,
                                audioProgress = uiState.trainerAudioProgress?.value,
                                onClickPlayAudio = {
                                    scope.launch {
                                        viewModel.playWorkoutIntroAudio()
                                    }
                                }
                            )
                        }
                    }

                workout.sections.forEach { section ->
                    stickyHeader(key = section.id) {
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .background(FutureTheme.colorScheme.gray90)
                                .padding(horizontal = 16.dp, vertical = 12.dp),
                            horizontalArrangement = Arrangement.SpaceBetween
                        ) {
                            Text(
                                text = section.name?.uppercase() ?: "",
                                fontSize = 12.sp,
                                fontWeight = FontWeight.SemiBold,
                                color = FutureTheme.colorScheme.textBlack,
                            )
                            Text(
                                text = section.totalDuration.naturalMinuteDurationString,
                                fontSize = 12.sp,
                                fontWeight = FontWeight.Regular,
                                color = FutureTheme.colorScheme.textBlack,
                            )
                        }
                    }

                    items(
                        key = { index -> section.exerciseSets[index].id },
                        count = section.exerciseSets.count()
                    ) { index ->
                        val exerciseSet = section.exerciseSets[index]
                        val exerciseSetSummary =
                            uiState.setSummaries.find { it.exerciseSet?.id == exerciseSet.id }
                        val exerciseFlag = uiState.flaggedExerciseMap[exerciseSet.id]
                        val customAudioUrl = exerciseSet.customAudioUrls.firstOrNull()
                        val audioProgress = uiState.trainerAudioProgress
                        val subtitle by remember(uiState.lastAdjustedExerciseSet, exerciseSet.id) {
                            // TODO: Investigate if this is a source of crashes
                            derivedStateOf { viewModel.getSubtitleForExerciseSet(exerciseSet) }
                        }
                        val isExpanded by remember(exerciseSet, expandedExerciseSet) {
                            derivedStateOf { expandedExerciseSet != null && expandedExerciseSet?.id == exerciseSet.id }
                        }

                        // if a workout is active, populate flag data using flagged exercises flow map,
                        // else populate flag data using exercise set summary
                        val isFlagged =
                            if (uiState.isWorkoutActive) {
                                exerciseFlag != null
                            } else {
                                exerciseSetSummary?.isFlagged
                                    ?: false
                            }
                        val flagTag = if (uiState.isWorkoutActive) {
                            exerciseFlag?.tags?.firstOrNull()
                        } else {
                            ExerciseSetFlagTag.getTagType(
                                exerciseSetSummary?.flaggedSetTags?.firstOrNull()
                            )
                        }
                        val flagNotes =
                            if (uiState.isWorkoutActive) exerciseFlag?.notes else exerciseSetSummary?.flaggedSetNotes

                        GuidedWorkoutOverviewExerciseSetRow(
                            exerciseSet = exerciseSet,
                            subtitle = subtitle,
                            exerciseSetProgress = uiState.currentExerciseSetProgress.toFloat(),
                            isCurrentExerciseSet = uiState.currentWorkoutState != ActiveWorkoutState.UNINITIALIZED &&
                                uiState.currentWorkoutState != ActiveWorkoutState.NOT_STARTED &&
                                uiState.currentWorkoutState != ActiveWorkoutState.FINISHED &&
                                exerciseSet.id == uiState.currentExerciseSet?.id,
                            isTransitioning = uiState.isTransitioning,
                            isCompleted = uiState.exerciseSetCompletionStates[exerciseSet.id] == ExerciseSetCompletionState.PARTIAL ||
                                uiState.exerciseSetCompletionStates[exerciseSet.id] == ExerciseSetCompletionState.FULL,
                            isWorkoutActive = uiState.isWorkoutActive,
                            showTrainerAudioButton = customAudioUrl != null,
                            currentTrainerImageUrl = uiState.currentTrainerImageUrl,
                            trainerAudioProgress = if (audioProgress?.mediaId == exerciseSet.id) audioProgress.value else 0f,
                            videoPlayer = videoPlayer,
                            shouldShowVideo = exerciseSet.videoUrl != null,
                            onExerciseControlButtonEventForExerciseSet = { buttonType: GuidedWorkoutExerciseControlButtonType ->
                                scope.launch {
                                    viewModel.onExerciseControlButtonEventForExerciseSet(
                                        buttonType,
                                        exerciseSet
                                    )

                                    // close expanded menu upon flagging so we can reset this menu
                                    when (buttonType) {
                                        GuidedWorkoutExerciseControlButtonType.ADD_FLAG, GuidedWorkoutExerciseControlButtonType.UPDATE_FLAG -> {
                                            expandedExerciseSet = null
                                        }

                                        // Dismiss overview if user has changed exercise set
                                        GuidedWorkoutExerciseControlButtonType.JUMP_TO_SET -> {
                                            onDismissWorkoutOverview()
                                        }

                                        GuidedWorkoutExerciseControlButtonType.EXERCISE_HISTORY -> {
                                            onExerciseHistoryClicked()
                                        }

                                        else -> {
                                        }
                                    }
                                }
                            },
                            onClickExerciseSetRow = {
                                scope.launch {
                                    expandedExerciseSet = if (isExpanded) null else exerciseSet
                                }
                            },
                            onClickTrainerAudioButton = {
                                scope.launch {
                                    viewModel.playTrainerAudioForExerciseSet(exerciseSet)
                                }
                            },
                            isExpanded = isExpanded,
                            isFlagged = isFlagged,
                            notes = flagNotes ?: "",
                            tag = ExerciseSetFlagTag.displayText(
                                flagTag ?: ExerciseSetFlagTagType.NONE
                            ),
                            isFullFeatureExperience = uiState.isFullFeatureExperience,
                            isExerciseHistoryEnabled = uiState.isExerciseHistoryEnabled,
                            isDistanceReportingEnabled = uiState.isDistanceReportingEnabled
                        )
                    }
                }
            }
        }
    }

    // Workout adjustment dialog
    uiState.displayedWorkoutAdjustmentDialog?.let { adjustmentDialog ->
        val type = adjustmentDialog.first
        val exerciseSet = adjustmentDialog.second
        when (type) {
            WorkoutAdjustmentDialogType.WEIGHT -> {
                WeightAdjustmentDialog(
                    exerciseSet = exerciseSet,
                    currentWeight = viewModel.getCurrentWeightForExerciseSet(exerciseSet),
                    onClickConfirm = { weight ->
                        viewModel.onUpdateWeightForExerciseSet(weight, exerciseSet)
                    },
                    onClickDismiss = {
                        viewModel.onDismissWorkoutAdjustmentDialog()
                    }
                )
            }

            WorkoutAdjustmentDialogType.REPS -> {
                RepAdjustmentDialog(
                    exerciseSet = exerciseSet,
                    currentReps = viewModel.getCurrentRepsForExerciseSet(exerciseSet),
                    onClickConfirm = { reps ->
                        viewModel.onUpdateRepsForExerciseSet(reps, exerciseSet)
                    },
                    onClickDismiss = {
                        viewModel.onDismissWorkoutAdjustmentDialog()
                    }
                )
            }

            WorkoutAdjustmentDialogType.DISTANCE -> {
                DistanceAdjustmentDialog(
                    exerciseSet = exerciseSet,
                    distanceUnitOptions = uiState.distanceUnitOptions,
                    currentDistance = viewModel.getCurrentDistanceForExerciseSet(exerciseSet),
                    onClickConfirm = { distance, units ->
                        viewModel.onUpdateDistanceForExerciseSet(distance, units, exerciseSet)
                    },
                    onClickDismiss = {
                        viewModel.onDismissWorkoutAdjustmentDialog()
                    }
                )
            }

            else -> {}
        }
    }

    if (modifyExerciseSheetState.isVisible) {
        val exerciseSet = uiState.selectedExerciseSet
        val setSummary = uiState.setSummaries.find { it.exerciseSet?.id == exerciseSet?.id }
        val exerciseFlag = uiState.flaggedExerciseMap[exerciseSet?.id]
        val flagTag = if (uiState.isWorkoutActive) {
            exerciseFlag?.tags?.firstOrNull()
        } else {
            ExerciseSetFlagTag.getTagType(
                setSummary?.flaggedSetTags?.firstOrNull()
            )
        }
        val flagNotes =
            if (uiState.isWorkoutActive) exerciseFlag?.notes else setSummary?.flaggedSetNotes

        ModifyExerciseBottomSheet(
            sheetState = modifyExerciseSheetState,
            exerciseSet = exerciseSet,
            notes = flagNotes ?: "",
            tag = flagTag ?: ExerciseSetFlagTagType.NONE,
            coachName = uiState.currentTrainerName ?: "",
            swappedExercise = uiState.currentExerciseSet?.swappedExercise,
            swappableExercises = uiState.swappableExercises,
            onUpdateClicked = { set, flag ->
                viewModel.onUpdateFlagForExerciseSet(
                    exerciseSet = set,
                    exerciseFlag = flag
                )
            },
            onRemoveFlagClicked = { set ->
                viewModel.showModifiedExerciseBottomSheet(false)
                viewModel.onRemoveFlagForExerciseSet(set)
                viewModel.resumeWorkout()
            },
            onReplaceExerciseClicked = { set, exercise, flag ->
                viewModel.showModifiedExerciseBottomSheet(false)
                viewModel.onReplaceExercise(set, exercise, flag)
                viewModel.resumeWorkout()
                onDismissWorkoutOverview()
                onExerciseSwapped()
            },
            onRevertExerciseClicked = { set ->
                viewModel.showModifiedExerciseBottomSheet(false)
                viewModel.onRevertExercise(set)
                viewModel.resumeWorkout()
                onDismissWorkoutOverview()
            },
            onCloseClicked = {
                viewModel.showModifiedExerciseBottomSheet(false)
                viewModel.resumeWorkout()
                viewModel.onCancelledExerciseSwap()
                onExerciseFlagged()
            }
        )
    }
}
