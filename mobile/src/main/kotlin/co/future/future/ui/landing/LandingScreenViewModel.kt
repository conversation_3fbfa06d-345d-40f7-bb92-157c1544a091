package co.future.future.ui.landing

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.widget.Toast
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.core.content.ContextCompat
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.lifecycle.viewmodel.compose.SavedStateHandleSaveableApi
import androidx.lifecycle.viewmodel.compose.saveable
import co.future.future.BuildConfig
import co.future.futurekit.constants.AppConstants
import co.future.future.data.auth.Authenticator
import co.future.future.data.device.DeviceRepository
import co.future.future.data.experiments.ExperimentsRepository
import co.future.future.data.lead.LeadRepository
import co.future.future.data.metricevent.MetricEvent
import co.future.future.data.metricevent.MetricEventRepository
import co.future.future.data.partnerships.PartnershipEntity
import co.future.future.data.partnerships.PartnershipsRepository
import co.future.future.data.referral.ReferralRepository
import co.future.future.data.userpreferences.UserPreferencesRepository
import co.future.future.data.userpreferences.getServerEnvironment
import co.future.futurekit.extensions.combine
import co.future.futurekit.extensions.unwrappedData
import co.future.future.ui.navigator.FutureNavigator
import co.future.future.ui.navigator.SurveyRoute
import co.future.future.utils.IoDispatcher
import co.future.futurekit.api.LeadApi
import co.future.futurekit.api.SessionApi
import co.future.futurekit.constants.FlowConstants
import co.future.futurekit.extensions.isValidEmail
import co.future.futurekit.extensions.unwrappedDataOrNull
import co.future.futurekit.models.AuthenticationState
import co.future.futurekit.models.Lead
import co.future.futurekit.models.Referral
import co.future.futurekit.models.ServerEnvironment
import co.future.futurekit.requests.SendEmailLinkRequest
import co.future.futurekit.requests.SendPhoneNumberLinkRequest
import co.future.futurekit.requests.TestTokenRequest
import com.google.i18n.phonenumbers.NumberParseException
import com.google.i18n.phonenumbers.PhoneNumberUtil
import com.google.mlkit.vision.barcode.common.Barcode
import com.google.mlkit.vision.codescanner.GmsBarcodeScannerOptions
import com.google.mlkit.vision.codescanner.GmsBarcodeScanning
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import timber.log.Timber
import java.util.*
import javax.inject.Inject

@SuppressLint("StaticFieldLeak")
@OptIn(SavedStateHandleSaveableApi::class)
@HiltViewModel
class LandingScreenViewModel @Inject constructor(
    @ApplicationContext private val applicationContext: Context,
    @IoDispatcher private val ioDispatcher: CoroutineDispatcher,
    savedStateHandle: SavedStateHandle,
    private val navigator: FutureNavigator,
    private val authenticator: Authenticator,
    private val leadApi: LeadApi,
    private val sessionApi: SessionApi,
    private val leadRepository: LeadRepository,
    private val deviceRepository: DeviceRepository,
    private val referralRepository: ReferralRepository,
    private val metricEventRepository: MetricEventRepository,
    private val partnershipsRepository: PartnershipsRepository,
    experimentsRepository: ExperimentsRepository,
    userPreferencesRepository: UserPreferencesRepository
) : ViewModel() {
    private var viewState by mutableStateOf<LandingScreenViewState>(LandingScreenViewState.GetStarted)
    private var inputType by mutableStateOf(LoginInputType.PHONE_NUMBER)
    private var email by savedStateHandle.saveable { mutableStateOf("") }
    private var phoneNumber by savedStateHandle.saveable { mutableStateOf("") }
    private var landingScreenError by mutableStateOf<Throwable?>(null)
    private var showWaitlistDialog by mutableStateOf(false)

    val uiState: StateFlow<LandingScreenUiState> = combine(
        snapshotFlow { viewState },
        snapshotFlow { inputType },
        snapshotFlow { email },
        snapshotFlow { phoneNumber },
        snapshotFlow { landingScreenError },
        snapshotFlow { showWaitlistDialog },
        referralRepository.organicReferralFlow,
        referralRepository.nonOrganicReferralFlow,
        userPreferencesRepository.preferencesFlow,
        experimentsRepository.getIsHyattWorkoutsEnabledFlow(),
    ) { viewState, inputType, email, phoneNumber, landingScreenError, showWaitlistDialog, organicReferral, nonOrganicReferral, preferences, isHyattWorkoutsEnabled ->
        // Only display serverEnvironmentName for non-production environments
        val serverEnvironment = preferences.getServerEnvironment()
        val serverEnvironmentName = if (serverEnvironment == ServerEnvironment.PRODUCTION) null else serverEnvironment.name.uppercase()

        LandingScreenUiState(
            viewState = viewState,
            inputType = inputType,
            email = email,
            phoneNumber = phoneNumber,
            launchReferral = nonOrganicReferral ?: organicReferral, // Prefer non-organic referral data on launch
            landingScreenError = landingScreenError,
            isQRCodeLoginEnabled = isHyattWorkoutsEnabled,
            showWaitlistDialog = showWaitlistDialog,
            serverEnvironmentName = serverEnvironmentName,
        )
    }
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(FlowConstants.KEEP_SUBSCRIPTION_ALIVE_DELAY),
            initialValue = LandingScreenUiState()
        )

    init {
        authenticator.authStateFlow
            .onEach { authState ->
                when (authState) {
                    is AuthenticationState.Authenticating -> {
                        viewState = LandingScreenViewState.Authenticating
                    }
                    is AuthenticationState.Success -> {
                        // Send signed in metric event
                        sendSignUpMetricEvent(
                            type = MetricEvent.SignUp.Type.SIGNED_IN,
                            signedInUserId = authState.userId
                        )
                    }
                    is AuthenticationState.SignedOut -> {
                        if (authState.error != null) {
                            landingScreenError = authState.error

                            // Send sign in failed metric event
                            sendSignUpMetricEvent(type = MetricEvent.SignUp.Type.SIGN_IN_FAILED)
                        }
                    }
                    else -> {}
                }
            }
            .launchIn(viewModelScope)
    }

    fun onClickGetStarted(clipboardText: String? = null) {
        // Allow authenticating from clipboard url right away on debug builds
        if (BuildConfig.DEBUG && clipboardText != null && clipboardText.startsWith("https://future.co/l/")) {
            val shortCode = Uri.parse(clipboardText).lastPathSegment
            if (shortCode != null) {
                authenticator.authenticateWithUrl(shortCode)
                return
            }
        }

        viewState = LandingScreenViewState.LoginInputForm()

        // Send sign-in selected metric
        sendSignUpMetricEvent(type = MetricEvent.SignUp.Type.SIGN_IN_SELECTED)
    }

    fun onClickSendSessionLink() {
        viewModelScope.launch(ioDispatcher) {
            viewState = LandingScreenViewState.LoginInputForm(isSendingSessionLink = true)

            try {
                if (inputType == LoginInputType.PHONE_NUMBER) {
                    // Parse and sanitize phone number before sending to our server
                    val sanitizedPhoneNumber = uiState.value.sanitizedPhoneNumber
                    if (AppConstants.TEST_PHONE_NUMBERS.contains(sanitizedPhoneNumber)) {
                        // Bypass auth for Google Play Store review accounts and certain testing accounts
                        val (userId, token) = sessionApi.getTestToken(
                            TestTokenRequest(phoneNumber = sanitizedPhoneNumber)
                        )
                            .unwrappedData()
                        authenticator.authenticateWithToken(token, userId)
                    } else {
                        // Send login link to phone number
                        sessionApi.sendPhoneNumberLink(SendPhoneNumberLinkRequest(phoneNumber = sanitizedPhoneNumber))
                    }
                } else {
                    // Send login link to email
                    sessionApi.sendEmailLink(SendEmailLinkRequest(email = email.lowercase()))
                }

                // Send link sent metric event
                sendSignUpMetricEvent(type = MetricEvent.SignUp.Type.LINK_SENT)

                // Show link set confirmation screen
                viewState = LandingScreenViewState.SessionLinkSentConfirmation
            } catch (error: Throwable) {
                if ((error as? retrofit2.HttpException)?.code() == 401) {
                    // Check if we are sold out
                    val availableTrainerIds = leadApi.getLeadAvailableTrainerIds(
                        platformId = AppConstants.FUTURE_ELITE_PLATFORM_ID
                    ).unwrappedDataOrNull()?.availableTrainerIds
                    if (availableTrainerIds != null && availableTrainerIds.isEmpty()) {
                        // Show waitlist
                        sendSignUpMetricEvent(type = MetricEvent.SignUp.Type.SHOW_WAITLIST)
                        Timber.tag(TAG).i("No available coaches, so showing the waitlist dialog")
                        showWaitlistDialog = true
                        viewState = LandingScreenViewState.LoginInputForm()
                    } else {
                        // Send find your coach metric event
                        sendSignUpMetricEvent(type = MetricEvent.SignUp.Type.FIND_YOUR_COACH)
                        Timber.tag(TAG).w("User does not exist - showing signup prompt")
                        viewState = LandingScreenViewState.SignupPrompt()
                    }
                } else {
                    Timber.tag(TAG).e("Error sending login link: ${error.localizedMessage} | $error")
                    landingScreenError = error
                    viewState = LandingScreenViewState.LoginInputForm()
                }
            }
        }
    }

    fun onToggleInputType() {
        inputType = if (inputType == LoginInputType.PHONE_NUMBER) {
            LoginInputType.EMAIL
        } else {
            LoginInputType.PHONE_NUMBER
        }
    }

    fun onScanQRCode(context: Context) {
        val options = GmsBarcodeScannerOptions.Builder()
            .setBarcodeFormats(Barcode.FORMAT_QR_CODE)
            .enableAutoZoom()
            .build()
        GmsBarcodeScanning.getClient(context, options)
            .startScan()
            .addOnSuccessListener { barcode ->
                val url = barcode.url?.url ?: barcode.rawValue
                if (url == null) {
                    Toast.makeText(context, "Failed to parse QR code", Toast.LENGTH_LONG).show()
                    return@addOnSuccessListener
                }

                Timber.tag(TAG).i("QR code url: $url")

                val segments = Uri.parse(url).pathSegments.toMutableList()

                // For partner links, add the "partner" segment to the path segments
                if (url.startsWith("x-future://partner")) {
                    segments.add(0, "partner")
                }

                when (segments.firstOrNull()) {
                    "l" -> {
                        // Authentication
                        authenticator.authenticateWithUrl(url)
                    }
                    "partner" -> {
                        // Ignore if we've handled partnership data already
                        if (partnershipsRepository.currentPartnershipFlow.value != null) {
                            Timber.tag(TAG).i("Ignoring partnership data as we've already handled it")
                            return@addOnSuccessListener
                        }

                        val partnershipEntity = PartnershipEntity.getPartnershipEntityFromId(segments.getOrNull(1))
                        if (partnershipEntity != null) {
                            val workoutCollectionCode = segments.getOrNull(2)
                            Timber.tag(
                                TAG
                            ).i("Setting partnership data: $partnershipEntity | code: $workoutCollectionCode")
                            partnershipsRepository.setCurrentPartnership(
                                partnership = partnershipEntity,
                                code = workoutCollectionCode
                            )
                        }
                    }
                    else -> {
                        Toast.makeText(context, "Invalid QR code", Toast.LENGTH_LONG).show()
                    }
                }
            }
            .addOnCanceledListener {
                Timber.tag(TAG).i("QR code scan canceled")
            }
            .addOnFailureListener { e ->
                Timber.tag(TAG).e(e, "Failed to scan QR code")
                Toast.makeText(context, "Failed to parse QR code", Toast.LENGTH_LONG).show()
            }
    }

    fun onClickSignUpOnWeb() {
        try {
            val intent = Intent(Intent.ACTION_VIEW, Uri.parse("https://www.future.co/"))
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            ContextCompat.startActivity(applicationContext, intent, null)
        } catch (error: Throwable) {
            Timber.tag(TAG).e("Error opening future homepage for signup: ${error.localizedMessage}")
        }
    }

    fun onClickSignUpInApp() {
        viewModelScope.launch(ioDispatcher) {
            viewState = LandingScreenViewState.SignupPrompt(isCreatingLead = true)
            try {
                // Create a new lead
                val newLead = leadRepository.updateLead(
                    lead = Lead(
                        platform = AppConstants.PLATFORM_NAME,
                        platformId = AppConstants.FUTURE_ELITE_PLATFORM_ID,
                        distinctId = deviceRepository.getDeviceUuid(),
                    ).apply {
                        // Assign pre-filled info from login input form
                        if (inputType == LoginInputType.PHONE_NUMBER) {
                            phoneNumber = uiState.value.sanitizedPhoneNumber
                        } else {
                            email = uiState.value.email
                        }

                        // Assign referral code info
                        uiState.value.launchReferral?.let { referral ->
                            referralCode = referral.code
                            couponId = referral.coupon.id
                        }
                    }
                )

                // Send entered information sign up metric event
                sendSignUpMetricEvent(
                    type = MetricEvent.SignUp.Type.ENTERED_INFORMATION,
                    screenType = if (inputType == LoginInputType.PHONE_NUMBER) "phone-number" else "email"
                )

                Timber.tag(TAG).i("Successfully created lead: $newLead. Starting survey flow...")
                navigator.navigate(SurveyRoute())
            } catch (error: Throwable) {
                Timber.tag(TAG).e("Error creating lead: $error")
                viewState = LandingScreenViewState.SignupPrompt()
                landingScreenError = error
            }
        }
    }

    fun onDismissLoginInputForm() {
        email = ""
        phoneNumber = ""
        viewState = LandingScreenViewState.GetStarted
    }

    fun onDismissSignUpPrompt() {
        email = ""
        phoneNumber = ""
        viewState = LandingScreenViewState.LoginInputForm()
    }

    fun onDismissLandingScreenError() {
        landingScreenError = null
    }

    fun onJoinWaitlist(fullName: String, email: String) {
        viewModelScope.launch(ioDispatcher) {
            try {
                // Create a new lead
                val newLead = leadRepository.updateLead(
                    lead = Lead(
                        platform = AppConstants.PLATFORM_NAME,
                        platformId = AppConstants.FUTURE_ELITE_PLATFORM_ID,
                        distinctId = deviceRepository.getDeviceUuid(),
                    ).apply {
                        // Assign pre-filled info from login input form
                        this.email = email
                        this.firstName = fullName.split(" ").getOrNull(0)
                        this.lastName = fullName.split(" ").getOrNull(1)
                        this.source = "android_sold_out_modal"

                        // Assign referral code info
                        uiState.value.launchReferral?.let { referral ->
                            this.referralCode = referral.code
                            this.couponId = referral.coupon.id
                        }
                    }
                )

                Timber.tag(TAG).i("Successfully joined waitlist for lead: $newLead.")
            } catch (error: Throwable) {
                Timber.tag(TAG).e(error, "Error creating lead.")
            }
        }
    }

    fun onDismissWaitlistDialog() {
        showWaitlistDialog = false
    }

    fun onUpdateEmail(email: String) {
        this.email = email
    }

    fun onUpdatePhoneNumber(phoneNumber: String) {
        this.phoneNumber = phoneNumber
    }

    private fun sendSignUpMetricEvent(
        type: MetricEvent.SignUp.Type,
        signedInUserId: String? = null,
        screenType: String? = null
    ) {
        val event = MetricEvent.SignUp(
            type = type,
            signUpLeadId = leadRepository.currentLeadFlow.value?.id,
            signedInUserId = signedInUserId,
            organicReferralCode = referralRepository.organicReferralFlow.value?.code,
            nonOrganicReferralCode = referralRepository.nonOrganicReferralFlow.value?.code,
            uniqueDeviceId = deviceRepository.deviceStateFlow.value?.id,
            screenType = screenType
        )
        metricEventRepository.sendMetricEvent(metricEvent = event, useAuthentication = false)
    }

    companion object {
        private const val TAG = "LandingScreenViewModel"
    }
}

data class LandingScreenUiState(
    val viewState: LandingScreenViewState = LandingScreenViewState.GetStarted,
    val inputType: LoginInputType = LoginInputType.PHONE_NUMBER,
    val email: String = "",
    val phoneNumber: String = "",
    val launchReferral: Referral? = null,
    val landingScreenError: Throwable? = null,
    val isQRCodeLoginEnabled: Boolean = false,
    val showWaitlistDialog: Boolean = false,
    val serverEnvironmentName: String? = null,
) {
    private val phoneUtil = PhoneNumberUtil.getInstance()
    private val countryCode: String = Locale.getDefault().country

    val isPastGetStarted: Boolean
        get() = viewState != LandingScreenViewState.GetStarted

    val inputValue: String
        get() {
            return if (inputType == LoginInputType.PHONE_NUMBER) phoneNumber else email
        }

    val formattedInputValue: String
        get() {
            return if (inputType == LoginInputType.PHONE_NUMBER) {
                try {
                    val parsedPhoneNumber = phoneUtil.parse(inputValue, countryCode)
                    phoneUtil.format(parsedPhoneNumber, PhoneNumberUtil.PhoneNumberFormat.NATIONAL)
                } catch (throwable: NumberParseException) {
                    ""
                }
            } else {
                email
            }
        }

    val isInputValid: Boolean
        get() {
            return if (inputType == LoginInputType.PHONE_NUMBER) {
                // In a test environment, allow any number
                if (BuildConfig.TESTING.get() && inputValue.isNotEmpty()) {
                    return true
                }

                try {
                    val parsedPhoneNumber = phoneUtil.parse(inputValue, countryCode)
                    return phoneUtil.isValidNumber(parsedPhoneNumber)
                } catch (throwable: NumberParseException) {
                    false
                }
            } else {
                inputValue.isValidEmail()
            }
        }

    val sanitizedPhoneNumber: String
        get() {
            val parsedPhoneNumber = phoneUtil.parse(phoneNumber, countryCode)
            return "+1${parsedPhoneNumber.nationalNumber}"
        }

    val campaignText: String?
        get() {
            return launchReferral?.coupon?.displayMessage
        }
}
