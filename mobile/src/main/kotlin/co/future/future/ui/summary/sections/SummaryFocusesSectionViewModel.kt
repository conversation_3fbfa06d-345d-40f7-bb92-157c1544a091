package co.future.future.ui.summary.sections

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import co.future.future.data.user.UserRepository
import co.future.future.ui.navigator.FutureNavigator
import co.future.future.ui.navigator.GoalEntriesRoute
import co.future.futurekit.constants.FlowConstants
import co.future.futurekit.models.Focus
import co.future.futurekit.models.Goal
import co.future.futurekit.models.TrainingPlan
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.stateIn
import timber.log.Timber
import javax.inject.Inject

@HiltViewModel
class SummaryFocusesSectionViewModel @Inject constructor(
    userRepository: UserRepository,
    private val navigator: FutureNavigator
) : ViewModel(), FutureNavigator by navigator {
    val uiState: StateFlow<SummaryFocusesSectionUiState> = combine(
        userRepository.goalsFlow,
        userRepository.trainingPlansFlow,
        userRepository.focusesFlow
    ) { goals, trainingPlans, focuses ->

        goals?.forEach {
            Timber.tag("SummaryFocusesSectionViewModel").d("Goal: ${it.name}, ${it.createdAt}")
        }

        SummaryFocusesSectionUiState(
            goals = goals,
            trainingPlan = trainingPlans?.firstOrNull(),
            focuses = focuses?.filter { !it.goals.isNullOrEmpty() }
        )
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(FlowConstants.KEEP_SUBSCRIPTION_ALIVE_DELAY),
        initialValue = SummaryFocusesSectionUiState()
    )

    fun onClickGoal(goal: Goal) {
        navigator.navigate(GoalEntriesRoute.createGoalEntriesRoute(goalId = goal.id))
    }
}

data class SummaryFocusesSectionUiState(
    val goals: List<Goal>? = null,
    val trainingPlan: TrainingPlan? = null,
    val focuses: List<Focus>? = null
)
