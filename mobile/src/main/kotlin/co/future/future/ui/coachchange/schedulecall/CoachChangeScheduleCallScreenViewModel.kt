package co.future.future.ui.coachchange.schedulecall

import android.content.Context
import android.widget.Toast
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import co.future.future.R
import co.future.future.data.callappointment.CallAppointmentRepository
import co.future.future.data.metricevent.MetricEvent
import co.future.future.data.metricevent.MetricEventRepository
import co.future.future.data.trainer.CoachChangeRepository
import co.future.future.data.trainer.TrainerRepository
import co.future.future.ui.navigator.CoachChangeCompletedRoute
import co.future.future.ui.navigator.FutureNavigator
import co.future.futurekit.constants.FlowConstants
import co.future.futurekit.extensions.combine
import co.future.futurekit.extensions.dayDate
import co.future.futurekit.models.AppointmentType
import co.future.futurekit.models.CallAppointment
import co.future.futurekit.models.Trainer
import co.future.futurekit.utils.DateUtils
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch
import timber.log.Timber
import java.time.ZonedDateTime
import javax.inject.Inject

@HiltViewModel
class CoachChangeCallScreenViewModel @Inject constructor(
    savedStateHandle: SavedStateHandle,
    private val navigator: FutureNavigator,
    private val coachChangeRepository: CoachChangeRepository,
    private val trainerRepository: TrainerRepository,
    private val callAppointmentRepository: CallAppointmentRepository,
    private val metricEventRepository: MetricEventRepository
) : ViewModel(), FutureNavigator by navigator {
    private var selectedTabIndex: Int by mutableStateOf(0)
    private var userSelectedTime: ZonedDateTime? by mutableStateOf(null)
    private var showScheduleErrorDialog by mutableStateOf(false)
    private var isSchedulingAppointment by mutableStateOf(false)
    private var showCoachSwitchConfirmationDialog by mutableStateOf(false)

    val uiState: StateFlow<CoachSwitchCallScreenState> = combine(
        trainerRepository.currentTrainerFlow,
        callAppointmentRepository.callAppointmentsFlow,
        coachChangeRepository.availableCallTimesResultFlow,
        snapshotFlow { selectedTabIndex },
        snapshotFlow { userSelectedTime },
        snapshotFlow { showScheduleErrorDialog },
        snapshotFlow { isSchedulingAppointment },
        snapshotFlow { showCoachSwitchConfirmationDialog },
        coachChangeRepository.selectedTrainerFlow,
        trainerRepository.recommendedTrainersFlow
    ) { trainer,
        appointments,
        availableTimesResult,
        selectedTabIndex,
        userSelectedTime,
        showScheduleErrorDialog,
        isSchedulingAppointment,
        showCoachSwitchConfirmationDialog,
        selectedTrainer,
        recommendedTrainers ->
        val availableTimes = availableTimesResult()?.mapNotNull { it.time }
        val availableDays = availableTimes?.distinctBy { it.dayDate().toLocalDate() }
        val availableTimesForSelectedDay = availableTimes?.filter {
            it.dayDate().toLocalDate() == availableDays?.get(selectedTabIndex)?.dayDate()
                ?.toLocalDate()
        }
        val existingAppointment = appointments?.firstOrNull {
            it.type == AppointmentType.APPOINTMENT && (
                it.scheduledAt
                    ?: DateUtils.distantPast
                ) > ZonedDateTime.now()
        }
        CoachSwitchCallScreenState(
            currentTrainer = trainer,
            selectedTrainer = selectedTrainer,
            availableDays = availableDays,
            availableTimesForSelectedDay = availableTimesForSelectedDay,
            selectedTabIndex = selectedTabIndex,
            userSelectedTime = userSelectedTime ?: existingAppointment?.scheduledAt,
            existingAppointment = existingAppointment,
            showScheduleErrorDialog = showScheduleErrorDialog,
            isSchedulingAppointment = isSchedulingAppointment,
            showCoachSwitchConfirmationDialog = showCoachSwitchConfirmationDialog
        )
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(FlowConstants.KEEP_SUBSCRIPTION_ALIVE_DELAY),
        initialValue = CoachSwitchCallScreenState()
    )

    fun updatedSelectedTab(tabIndex: Int) {
        selectedTabIndex = tabIndex
    }

    fun updateUserSelectedTime(selectedTime: ZonedDateTime?) {
        val existingAppointmentTime = uiState.value.existingAppointment?.scheduledAt
        userSelectedTime = if (selectedTime != existingAppointmentTime) selectedTime else null

        Timber.tag(TAG)
            .i("User selected time: $userSelectedTime | existing time: $existingAppointmentTime")
    }

    fun showScheduleErrorDialog(show: Boolean) {
        showScheduleErrorDialog = show
    }

    fun confirmCoachSwitch(context: Context, callDate: ZonedDateTime?) {
        viewModelScope.launch {
            showCoachSwitchConfirmationDialog = false
            coachChangeRepository.submitCoachChange(callDate)

            val trainer = coachChangeRepository.selectedTrainerFlow.value
            metricEventRepository.sendMetricEvent(
                metricEvent = MetricEvent.CoachChange(
                    type = MetricEvent.CoachChange.Type.SCHEDULE_CALL,
                    trainerId = trainer?.id
                )
            )

            Toast.makeText(
                context,
                R.string.coach_switch_completed_toast_message,
                Toast.LENGTH_LONG
            ).show()

            navigator.navigate(CoachChangeCompletedRoute())
        }
    }

    fun showConfirmDialog(show: Boolean) {
        showCoachSwitchConfirmationDialog = show
    }

    companion object {
        const val TAG = "CoachSwitchCallScreenViewModel"
    }
}

data class CoachSwitchCallScreenState(
    val currentTrainer: Trainer? = null,
    val selectedTrainer: Trainer? = null,
    val availableDays: List<ZonedDateTime>? = null,
    val availableTimesForSelectedDay: List<ZonedDateTime>? = null,
    val selectedTabIndex: Int = 0,
    val userSelectedTime: ZonedDateTime? = null,
    val existingAppointment: CallAppointment? = null,
    val showScheduleErrorDialog: Boolean = false,
    val isSchedulingAppointment: Boolean = false,
    val showCoachSwitchConfirmationDialog: Boolean = false
) {
    val hasNoAvailableTimes: Boolean
        get() = availableDays != null && availableDays.isEmpty()
}
