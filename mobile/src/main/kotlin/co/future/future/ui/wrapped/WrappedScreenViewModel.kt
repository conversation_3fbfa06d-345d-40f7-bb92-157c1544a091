package co.future.future.ui.wrapped

import android.content.Context
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.graphics.ImageBitmap
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.media3.datasource.DefaultHttpDataSource
import androidx.media3.datasource.cache.CacheDataSource
import androidx.media3.datasource.cache.SimpleCache
import androidx.media3.exoplayer.source.DefaultMediaSourceFactory
import androidx.media3.exoplayer.source.MediaSource
import co.future.future.data.media.MediaUploadRepository
import co.future.future.data.metricevent.MetricEvent
import co.future.future.data.metricevent.MetricEventRepository
import co.future.future.data.user.UserRepository
import co.future.future.ui.navigator.FutureNavigator
import co.future.future.ui.navigator.ShareWrappedRoute
import co.future.future.utils.IoDispatcher
import co.future.future.utils.MainDispatcher
import co.future.futurekit.constants.FlowConstants
import co.future.futurekit.models.CaloriesBurned
import co.future.futurekit.models.Trainer
import co.future.futurekit.models.WrappedStats
import coil.annotation.ExperimentalCoilApi
import coil.imageLoader
import coil.request.ErrorResult
import coil.request.ImageRequest
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.distinctUntilChangedBy
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.mapNotNull
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import timber.log.Timber
import java.text.SimpleDateFormat
import java.util.*
import javax.inject.Inject
import kotlin.math.max

@HiltViewModel
class WrappedScreenViewModel @Inject constructor(
    @ApplicationContext private val applicationContext: Context,
    @MainDispatcher private val mainDispatcher: CoroutineDispatcher,
    @IoDispatcher private val ioDispatcher: CoroutineDispatcher,
    private val navigator: FutureNavigator,
    userRepository: UserRepository,
    private val metricEventRepository: MetricEventRepository,
    private val mediaUploadRepository: MediaUploadRepository,
    mediaCache: SimpleCache
) : ViewModel(), FutureNavigator by navigator {
    private val mainScope = CoroutineScope(Job() + mainDispatcher)

    // Cache factories
    private val cacheDataSourceFactory: CacheDataSource.Factory by lazy {
        val httpDataSourceFactory = DefaultHttpDataSource.Factory()
            .setAllowCrossProtocolRedirects(true)
        CacheDataSource.Factory()
            .setCache(mediaCache)
            .setUpstreamDataSourceFactory(httpDataSourceFactory)
            .setFlags(CacheDataSource.FLAG_IGNORE_CACHE_ON_ERROR)
    }
    val mediaSourceFactory: MediaSource.Factory by lazy {
        DefaultMediaSourceFactory(cacheDataSourceFactory)
    }

    private var allPages by mutableStateOf<List<WrappedPage>>(defaultPagesList)
    private var currentPageIndex by mutableIntStateOf(0)
    private var activePageTimer by mutableFloatStateOf(0.0f) // 0-1s
    private var activePageTimerJob: Job? = null

    val uiState: StateFlow<WrappedScreenUiState> = combine(
        snapshotFlow { allPages },
        snapshotFlow { currentPageIndex },
        snapshotFlow { activePageTimer }
    ) { allPages, currentPageIndex, activePageTimer ->
        WrappedScreenUiState(
            currentPage = allPages[currentPageIndex],
            currentPageIndex = currentPageIndex,
            totalPageCount = max(allPages.size, 1),
            activePageTimer = activePageTimer
        )
    }
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(FlowConstants.KEEP_SUBSCRIPTION_ALIVE_DELAY),
            initialValue = WrappedScreenUiState()
        )

    init {
        Timber.tag(TAG).i("Initializing WrappedScreenViewModel...")
        userRepository
            .wrappedStatsFlow
            .mapNotNull { it }
            .distinctUntilChangedBy { it.id }
            .onEach {
                Timber.tag(TAG).i("Populating pages list...")
                val list = populatePagesList(it)

                // Do not start active page timer job if first page is video intro
                if (list.firstOrNull() is WrappedPage.IntroVideo) {
                    activePageTimerJob?.cancel()
                    activePageTimer = 0.0f
                } else {
                    startActivePageTimer()
                }
            }
            .launchIn(viewModelScope)

        uiState
            .map { it.currentPage }
            .distinctUntilChangedBy { it.id }
            .onEach {
                metricEventRepository.sendMetricEvent(
                    MetricEvent.FutureWrapped(type = "viewed", screen = it.id)
                )
            }
            .launchIn(viewModelScope)
    }

    override fun onCleared() {
        super.onCleared()

        activePageTimerJob?.cancel()
    }

    fun startActivePageTimer() {
        activePageTimerJob?.cancel()
        activePageTimerJob = mainScope.launch {
            while (isActive) {
                delay(1000L)

                if (activePageTimer >= 1.0f) {
                    advancePage()
                } else {
                    activePageTimer += 0.1f
                }
            }
        }
    }

    fun pauseActivePageTimer() {
        activePageTimerJob?.cancel()
    }

    fun onVideoProgress(progress: Float, totalDuration: Long) {
        activePageTimer = progress
    }

    fun advancePage() {
        if (currentPageIndex < allPages.size - 1) {
            activePageTimer = 0.0f
            currentPageIndex += 1

            // Restart timer if is deactivated
            if (activePageTimerJob == null || activePageTimerJob?.isActive != true) {
                startActivePageTimer()
            }
        } else {
            popBackStack()
        }
    }

    fun regressPage() {
        if (currentPageIndex > 0) {
            activePageTimer = 0.0f
            currentPageIndex -= 1

            // Restart timer if is deactivated
            if (activePageTimerJob == null || activePageTimerJob?.isActive != true && allPages.firstOrNull() !is WrappedPage.IntroVideo) {
                startActivePageTimer()
            }
        } else {
            activePageTimerJob?.cancel()
            activePageTimer = 0.0f
        }
    }

    fun onClickShare() {
        // Cancel timer
        activePageTimerJob?.cancel()

        // Send metrics
        metricEventRepository.sendMetricEvent(
            MetricEvent.FutureWrapped(type = "shared", screen = uiState.value.currentPage.id)
        )

        // Navigate to our custom share sheet
        navigator.navigate(ShareWrappedRoute())
    }

    fun saveScreenShot(imageBitmap: ImageBitmap) {
        viewModelScope.launch {
            mediaUploadRepository.saveScreenShot(imageBitmap)
        }
    }

    fun onClickDismiss() {
        popBackStack()
    }

    fun onClickSaveAllImages() {
        // Send metrics
        metricEventRepository.sendMetricEvent(
            MetricEvent.FutureWrapped(type = "save-all")
        )

        // TODO: Save all images to gallery... might not be trivial
    }

    fun onClickReplayFromBeginning() {
        // Send metrics
        metricEventRepository.sendMetricEvent(
            MetricEvent.FutureWrapped(type = "replay")
        )

        activePageTimerJob?.cancel()
        activePageTimer = 0.0f
        currentPageIndex = 0

        startActivePageTimer()
    }

    private fun populatePagesList(wrappedStats: WrappedStats, trainer: Trainer? = null): List<WrappedPage> {
        val list = mutableListOf<WrappedPage>()
        val trainerImageUrl = wrappedStats.coachImageUrl ?: trainer?.circleWhiteImageUrl ?: trainer?.circleImageUrl ?: trainer?.images?.firstOrNull()?.url
        preloadImageUrl(trainerImageUrl)

        // ✅ Intro
        val userFirstName = wrappedStats.firstName
        val videoMessageAttachment = wrappedStats.coachMessage?.attachments?.find { it.isVideo() }
        val videoUrl = videoMessageAttachment?.url
        if (videoUrl != null) {
            list.add(
                WrappedPage.IntroVideo(
                    userFirstName = userFirstName ?: "Hi",
                    videoUrl = videoUrl,
                    videoThumbnailUrl = videoMessageAttachment.thumbnailUrl,
                    videoContentType = videoMessageAttachment.contentType,
                    videoWidth = videoMessageAttachment.width,
                    videoHeight = videoMessageAttachment.height
                )
            )
        } else {
            list.add(
                WrappedPage.Intro(
                    userFirstName = userFirstName ?: "Hi",
                    trainerImageUrl = trainerImageUrl
                )
            )
        }

        // ✅ When screens
        val totalWorkoutCount = wrappedStats.totalWorkoutCount ?: 0
        val workoutTime = wrappedStats.workoutTime
        if (totalWorkoutCount >= 1 && workoutTime != null) {
            // ✅ When title
            list.add(
                WrappedPage.WhenTitle(
                    totalWorkoutCount = totalWorkoutCount
                )
            )

            // ✅ When day-by-day
            val workoutDaysIndices = workoutTime.workoutDays?.mapNotNull(::convertDateStringToIndices)
            val pausedDaysIndices = workoutTime.pausedDays?.mapNotNull(::convertDateStringToIndices)
            list.add(
                WrappedPage.WhenDayByDay(
                    numWorkoutDays = workoutTime.numWorkoutDays ?: 0,
                    numMembershipDays = workoutTime.numMembershipDays ?: 0,
                    workoutDaysIndices = workoutDaysIndices ?: emptyList(),
                    pausedDaysIndices = pausedDaysIndices ?: emptyList(),
                    bestMonth = workoutTime.bestMonth?.cluster ?: "",
                    numWorkoutsForBestMonth = workoutTime.bestMonth?.count ?: 0,
                    bestDay = workoutTime.bestDay?.cluster ?: "",
                    numWorkoutsForBestDay = workoutTime.bestDay?.count ?: 0
                )
            )

            // ✅ When hour-by-hour
            val workoutTimeSegments = workoutTime.topWorkoutTimes?.map { Pair(it.cluster ?: "", it.count ?: 0) } ?: emptyList()
            val earliestWorkoutDate = convertDateStringToMonthDayString(workoutTime.earliestWorkout?.date) ?: ""
            val latestWorkoutDate = convertDateStringToMonthDayString(workoutTime.latestWorkout?.date) ?: ""
            preloadImageUrl(workoutTime.clockImageUrl)
            list.add(
                WrappedPage.WhenHourByHour(
                    workoutTimeSegments = workoutTimeSegments,
                    earliestWorkoutDate = earliestWorkoutDate,
                    earliestWorkoutTime = workoutTime.earliestWorkout?.time ?: "",
                    latestWorkoutDate = latestWorkoutDate,
                    latestWorkoutTime = workoutTime.latestWorkout?.time ?: "",
                    clockImageUrl = workoutTime.clockImageUrl
                )
            )
        }

        // ✅ Where screens
        val workoutLocation = wrappedStats.workoutLocation
        val states = workoutLocation?.states ?: emptyList()
        val countries = workoutLocation?.countries?.filterNot { it == "United States" } ?: emptyList()
        if (states.isNotEmpty() || countries.isNotEmpty()) {
            val usMapImageUrl = workoutLocation?.usMapImageUrl
            val worldMapImageUrl = workoutLocation?.worldMapImageUrl
            preloadImageUrl(usMapImageUrl)
            preloadImageUrl(worldMapImageUrl)

            // ✅ Where title
            list.add(
                WrappedPage.WhereTitle()
            )

            // ✅ Where state / us / world
            if (countries.isNotEmpty()) {
                list.add(
                    WrappedPage.WhereWorld(
                        states = states,
                        countries = countries,
                        usMapImageUrl = usMapImageUrl,
                        worldMapImageUrl = worldMapImageUrl
                    )
                )
            } else if (states.count() >= 2) {
                list.add(
                    WrappedPage.WhereUS(
                        states = states,
                        usMapImageUrl = usMapImageUrl
                    )
                )
            } else {
                list.add(
                    WrappedPage.WhereState(
                        state = states.first(),
                        usMapImageUrl = usMapImageUrl
                    )
                )
            }
        }

        // ✅ How screens
        val workoutActivity = wrappedStats.workoutActivity
        if (workoutActivity != null) {
            // ✅ How title
            list.add(
                WrappedPage.HowTitle(
                    totalWorkoutCount = totalWorkoutCount
                )
            )

            // ✅ How relative
            val totalDurationMinutes = workoutActivity.totalDurationMinutes ?: 0
            if (totalDurationMinutes >= WrappedPage.breakingMinutes) {
                list.add(
                    WrappedPage.HowRelative(
                        totalDurationMinutes = totalDurationMinutes,
                    )
                )
            }

            // ✅ How scatter graph
            val caloriesBurned = workoutActivity.caloriesBurned ?: emptyList()
            if (caloriesBurned.isNotEmpty()) {
                val updatedCaloriesBurned = caloriesBurned.toMutableList()
                workoutActivity.averageWorkout?.let {
                    updatedCaloriesBurned.add(
                        CaloriesBurned(duration = it.duration, calories = it.calories, isAverageWorkout = true)
                    )
                }
                workoutActivity.longestWorkout?.let {
                    updatedCaloriesBurned.add(
                        CaloriesBurned(duration = it.duration, calories = it.calories, isLongestWorkout = true)
                    )
                }
                workoutActivity.mostAerobicWorkout?.let {
                    updatedCaloriesBurned.add(
                        CaloriesBurned(duration = it.duration, calories = it.calories, isHighestCalorieBurn = true)
                    )
                }
                list.add(
                    WrappedPage.HowScatter(
                        caloriesBurned = updatedCaloriesBurned,
                        averageWorkout = workoutActivity.averageWorkout,
                        longestWorkout = workoutActivity.longestWorkout,
                        mostAerobicWorkout = workoutActivity.mostAerobicWorkout
                    )
                )
            }

            // ✅ How exercises / reps
            val exercises = workoutActivity.exercises
            if (
                exercises != null &&
                (exercises.topExercises?.count() ?: 0) >= 0 &&
                (exercises.totalReps ?: 0) >= 0
            ) {
                list.add(
                    WrappedPage.HowReps(
                        topExercises = exercises.topExercises ?: emptyList(),
                        totalMovements = exercises.movementCount ?: 0,
                        totalReps = exercises.totalReps ?: 0,
                        allOtherTotalReps = exercises.allOtherTotalReps ?: 0
                    )
                )
            }

            // ✅ How volume
            val volume = workoutActivity.volume
            if (volume != null && (volume.totalVolume ?: 0) >= WrappedPage.hippoWeightPounds) {
                preloadImageUrl(volume.simileImageUrl)
                list.add(
                    WrappedPage.HowVolume(
                        topExercises = volume.topExercises ?: emptyList(),
                        totalVolume = volume.totalVolume ?: 0,
                        allOtherTotalVolume = volume.allOtherTotalVolume ?: 0
                    )
                )
            }

            // ✅ How heart rate zones
            val heartRateZones = workoutActivity.heartRateZones ?: emptyList()
            val totalMinutes = heartRateZones.sumOf { it.durationMinutes ?: 0 }
            if (totalMinutes > 0) {
                list.add(
                    WrappedPage.HowHeartRateZone(
                        heartRateZones = heartRateZones
                    )
                )
            }

            // ✅ How mileage
            val distance = workoutActivity.distance
            if (distance != null && (distance.totalMileage ?: 0.0) >= 10) {
                val milesByMonth = WrappedPage.monthToString.map { month ->
                    val distanceMiles = distance.monthDistances?.find { it.month == month.key }?.distanceMiles ?: 0.0
                    Pair(month.value, distanceMiles)
                }
                list.add(
                    WrappedPage.HowMileage(
                        milesByMonth = milesByMonth,
                        totalMileage = distance.totalMileage ?: 0.0,
                        averageDistance = distance.averageDistance ?: 0.0,
                        longestDistance = distance.longestDistance ?: 0.0,
                        longestDistanceDateString = convertDateStringToMonthDayString(distance.longestDistanceDate) ?: ""
                    )
                )
            }
        }

        // ✅ Coach screens
        val coachCommunication = wrappedStats.coachCommunication
        if (coachCommunication != null) {
            // ✅ Coach title
            val hasMultipleCoaches = (coachCommunication.coachesCount ?: 1) > 1
            list.add(
                WrappedPage.CoachTitle(
                    hasMultipleCoaches = hasMultipleCoaches
                )
            )

            // ✅ Coach messages
            val messageCount = coachCommunication.messageCount ?: 0
            val totalWords = coachCommunication.totalWords ?: 0
            val totalCharacters = coachCommunication.totalCharacters ?: 0
            val messagesPercentByDayMap = coachCommunication.messagesPercentByDay
            val messagesPercentByDay = listOf(
                Pair("Mon", messagesPercentByDayMap?.percentMonday ?: 0),
                Pair("Tue", messagesPercentByDayMap?.percentTuesday ?: 0),
                Pair("Wed", messagesPercentByDayMap?.percentWednesday ?: 0),
                Pair("Thu", messagesPercentByDayMap?.percentThursday ?: 0),
                Pair("Fri", messagesPercentByDayMap?.percentFriday ?: 0),
                Pair("Sat", messagesPercentByDayMap?.percentSaturday ?: 0),
                Pair("Sun", messagesPercentByDayMap?.percentSunday ?: 0)
            )
            list.add(
                WrappedPage.CoachMessages(
                    hasMultipleCoaches = hasMultipleCoaches,
                    messagesPercentByDay = messagesPercentByDay,
                    messageCount = messageCount,
                    totalWords = totalWords,
                    totalCharacters = totalCharacters
                )
            )

            // ✅ Coach voiceovers
            val voiceoversCount = coachCommunication.voiceCueCount ?: 0
            if (voiceoversCount > 1) {
                list.add(
                    WrappedPage.CoachVoiceovers(
                        trainerImageUrl = trainerImageUrl,
                        hasMultipleCoaches = hasMultipleCoaches,
                        voiceoversCount = voiceoversCount
                    )
                )
            }

            // ✅ Coach photos
            val photoGridUrl = coachCommunication.photoGridUrl
            val imageCount = coachCommunication.imageCount ?: 0
            if (photoGridUrl != null && imageCount >= 21) {
                preloadImageUrl(photoGridUrl)
                list.add(
                    WrappedPage.CoachPhotos(
                        hasMultipleCoaches = hasMultipleCoaches,
                        photoGridUrl = photoGridUrl,
                        imageCount = imageCount
                    )
                )
            }
        }

        // ✅ Conclusion
        list.add(
            WrappedPage.Conclusion()
        )

        allPages = list
        return list
    }

    private fun convertDateStringToIndices(dateString: String): Int? {
        val components = dateString.split("-")
        try {
            val monthIndex = components[1].toInt() - 1
            val dayIndex = components[2].toInt() - 1
            return monthIndex * WrappedPage.numDaysByIndex[monthIndex]!! + dayIndex
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return null
    }

    private fun convertDateStringToMonthDayString(inputDateString: String?): String? {
        if (inputDateString == null) return null

        val inputFormat = SimpleDateFormat("yyyy-MM-dd", Locale.US)
        val inputDate = inputFormat.parse(inputDateString) ?: return null
        return SimpleDateFormat("MMMM d", Locale.US).format(inputDate)
    }

    @OptIn(ExperimentalCoilApi::class)
    private fun preloadImageUrl(imageUrl: String? = null) {
        if (imageUrl == null) return

        viewModelScope.launch(ioDispatcher) {
            val cachedImage = applicationContext.imageLoader.diskCache?.openSnapshot(imageUrl)
            if (cachedImage != null) {
                cachedImage.close()
            } else {
                val request = ImageRequest.Builder(applicationContext)
                    .data(imageUrl)
                    .diskCacheKey(imageUrl)
                    .build()
                val result = applicationContext.imageLoader.execute(request)
                if (result is ErrorResult) {
                    val error = result.throwable
                    Timber.tag(TAG).e(error, "Encountered error downloading image url: $imageUrl.")
                }
            }
        }
    }

    companion object {
        private const val TAG = "WrappedScreenViewModel"
        private val defaultPagesList = listOf(
            WrappedPage.Intro(userFirstName = "Hi")
        )
    }
}

data class WrappedScreenUiState(
    val currentPage: WrappedPage = WrappedPage.Intro(userFirstName = "Hi"),
    val currentPageIndex: Int = 0,
    val totalPageCount: Int = 1,
    val activePageTimer: Float = 0.0f // 0-1s
)
