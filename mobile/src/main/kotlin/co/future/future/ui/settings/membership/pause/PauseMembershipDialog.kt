package co.future.future.ui.settings.membership.pause

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.outlined.PausePresentation
import androidx.compose.material.icons.outlined.SmartDisplay
import androidx.compose.material3.BasicAlertDialog
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.graphics.RectangleShape
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import co.future.future.R
import co.future.futuredesign.FutureTheme
import co.future.futuredesign.PreviewThemeProvider

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PauseMembershipDialog(
    pauseDate: String,
    resumeDate: String,
    onConfirm: () -> Unit,
    onDismiss: () -> Unit
) {
    BasicAlertDialog(
        onDismissRequest = {
            onDismiss()
        },
        content = {
            Surface(
                contentColor = FutureTheme.colorScheme.contentBackground,
                shape = RoundedCornerShape(8.dp)
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp)
                        .wrapContentHeight(),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = stringResource(id = R.string.membership_pause_dialog_title).uppercase(),
                        fontSize = 12.sp,
                        color = FutureTheme.colorScheme.textGray,
                        fontWeight = FontWeight.SemiBold,
                        letterSpacing = 1.5.sp
                    )

                    Spacer(modifier = Modifier.size(32.dp))

                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = 16.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Image(
                            imageVector = Icons.Outlined.PausePresentation,
                            contentDescription = "",
                            modifier = Modifier.size(36.dp),
                            colorFilter = ColorFilter.tint(FutureTheme.colorScheme.mint)
                        )
                        Column(modifier = Modifier.padding(horizontal = 16.dp)) {
                            Text(
                                text = stringResource(id = R.string.membership_pause_dialog_start_title),
                                fontSize = 13.sp,
                                color = FutureTheme.colorScheme.textGray,
                                modifier = Modifier.padding(bottom = 4.dp)
                            )

                            Text(
                                text = pauseDate,
                                fontSize = 17.sp,
                                fontWeight = FontWeight.SemiBold,
                                color = FutureTheme.colorScheme.textBlack
                            )
                        }
                    }

                    Spacer(modifier = Modifier.size(24.dp))
                    HorizontalDivider(color = FutureTheme.colorScheme.divider)
                    Spacer(modifier = Modifier.size(24.dp))

                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = 16.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Image(
                            imageVector = Icons.Outlined.SmartDisplay,
                            contentDescription = "",
                            modifier = Modifier.size(36.dp),
                            colorFilter = ColorFilter.tint(FutureTheme.colorScheme.mint)
                        )

                        Column(modifier = Modifier.padding(horizontal = 16.dp)) {
                            Text(
                                text = stringResource(id = R.string.membership_pause_dialog_end_title),
                                fontSize = 13.sp,
                                color = FutureTheme.colorScheme.textGray,
                                modifier = Modifier.padding(bottom = 4.dp)
                            )

                            Text(
                                text = resumeDate,
                                fontSize = 17.sp,
                                fontWeight = FontWeight.SemiBold,
                                color = FutureTheme.colorScheme.textBlack
                            )
                        }
                    }

                    Spacer(modifier = Modifier.size(32.dp))

                    Button(
                        onClick = onConfirm,
                        colors = ButtonDefaults.buttonColors(containerColor = FutureTheme.colorScheme.black),
                        shape = RectangleShape,
                        modifier = Modifier
                            .fillMaxWidth()
                    ) {
                        Text(
                            text = stringResource(id = R.string.membership_pause_dialog_positive_button_text),
                            color = FutureTheme.colorScheme.white,
                            fontSize = 13.sp,
                            modifier = Modifier.padding(vertical = 8.dp)
                        )
                    }

                    Spacer(modifier = Modifier.size(16.dp))

                    Button(
                        onClick = onDismiss,
                        colors = ButtonDefaults.buttonColors(containerColor = FutureTheme.colorScheme.gray90),
                        shape = RectangleShape,
                        modifier = Modifier
                            .fillMaxWidth()
                    ) {
                        Text(
                            text = stringResource(id = R.string.membership_pause_dialog_negative_button_text),
                            color = FutureTheme.colorScheme.black,
                            fontSize = 13.sp,
                            modifier = Modifier.padding(vertical = 8.dp)
                        )
                    }
                }
            }
        }
    )
}

@Preview
@Composable
fun PreviewPauseMembershipDialog() {
    PreviewThemeProvider {
        PauseMembershipDialog(
            pauseDate = "Monday, Jul 8",
            resumeDate = "Monday, Aug 12",
            onConfirm = {},
            onDismiss = {}
        )
    }
}
