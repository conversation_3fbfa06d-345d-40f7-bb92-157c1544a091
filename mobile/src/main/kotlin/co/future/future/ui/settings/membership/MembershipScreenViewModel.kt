package co.future.future.ui.settings.membership

import android.annotation.SuppressLint
import android.content.Context
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import co.future.future.R
import co.future.future.data.billing.BillingRepository
import co.future.future.data.feature.FeatureRepository
import co.future.future.data.user.UserRepository
import co.future.future.ui.navigator.CancelMembershipRoute
import co.future.future.ui.navigator.ChangeMembershipPlanRoute
import co.future.future.ui.navigator.FutureNavigator
import co.future.future.ui.navigator.PauseMembershipRoute
import co.future.futurekit.constants.FlowConstants
import co.future.futurekit.extensions.combine
import co.future.futurekit.utils.DateUtils
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch
import java.time.ZonedDateTime
import javax.inject.Inject

@SuppressLint("StaticFieldLeak")
@HiltViewModel
class MembershipScreenViewModel @Inject constructor(
    @ApplicationContext private val applicationContext: Context,
    private val navigator: FutureNavigator,
    private val userRepository: UserRepository,
    billingRepository: BillingRepository,
    featureRepository: FeatureRepository,
) : ViewModel(), FutureNavigator by navigator {
    private var showResumeMembershipDialog by mutableStateOf(false)

    val uiState: StateFlow<MembershipScreenUiState> = combine(
        userRepository.currentUserFlow,
        billingRepository.membershipRenewalDateFlow,
        billingRepository.currentSubscriptionFlow,
        billingRepository.effectiveSubscriptionPriceFlow,
        snapshotFlow { showResumeMembershipDialog }
    ) { currentUser, membershipRenewalDate, currentSubscription, effectiveSubscriptionPrice, showResumeMembershipDialog ->
        val user = currentUser ?: return@combine MembershipScreenUiState()

        val memberSinceDateString = DateUtils.monthYearFormatter.format(user.createdAt)
        val membershipSinceText = applicationContext.getString(
            R.string.membership_member_since_text,
            memberSinceDateString
        )

        val appLogoAlpha: Float
        val showMembershipCheckmark: Boolean
        val statusTitle: String
        val membershipPlanDetails: String

        when {
            // Pending cancellation
            (user.endedAt ?: DateUtils.distantPast) > ZonedDateTime.now() -> {
                appLogoAlpha = 0.66f
                showMembershipCheckmark = false
                statusTitle = applicationContext.getString(R.string.membership_status_pending_cancellation)
                membershipPlanDetails = applicationContext.getString(
                    R.string.membership_pending_cancellation_text,
                    DateUtils.monthDayFormatter.format(user.endedAt)
                )
            }

            // Pending pause / paused
            (user.unpausedAt ?: DateUtils.distantPast) > ZonedDateTime.now() -> {
                if ((user.pausedAt ?: DateUtils.distantPast) > ZonedDateTime.now()) {
                    appLogoAlpha = 1f
                    showMembershipCheckmark = true
                    statusTitle = applicationContext.getString(R.string.membership_status_pending_pause)
                    membershipPlanDetails = applicationContext.getString(
                        R.string.membership_pending_pause_text,
                        DateUtils.monthDayFormatter.format(user.pausedAt)
                    )
                } else {
                    appLogoAlpha = 0.66f
                    showMembershipCheckmark = false
                    statusTitle = applicationContext.getString(R.string.membership_status_paused)
                    membershipPlanDetails = applicationContext.getString(
                        R.string.membership_paused_text,
                        DateUtils.monthDayFormatter.format(user.unpausedAt)
                    )
                }
            }

            // Active
            else -> {
                appLogoAlpha = 1f
                showMembershipCheckmark = true
                statusTitle = applicationContext.getString(R.string.membership_status_active)
                membershipPlanDetails = if ((membershipRenewalDate ?: DateUtils.distantPast) > ZonedDateTime.now()) {
                    val prefixStringResId = if (currentSubscription?.price?.id != effectiveSubscriptionPrice?.id) {
                        R.string.membership_starts_on_text
                    } else {
                        R.string.membership_renews_on_text
                    }
                    applicationContext.getString(
                        prefixStringResId,
                        DateUtils.simpleMonthDateYearFormatter.format(membershipRenewalDate)
                    )
                } else {
                    membershipSinceText
                }
            }
        }

        MembershipScreenUiState(
            appLogoAlpha = appLogoAlpha,
            showMembershipCheckmark = showMembershipCheckmark,
            membershipSinceText = membershipSinceText,
            statusTitle = statusTitle,
            membershipPlanTitle = applicationContext.getString(
                R.string.membership_plan_plan_text,
                effectiveSubscriptionPrice?.resolvedType?.membershipTitle ?: "Future"
            ),
            membershipPlanDetails = membershipPlanDetails,
            showResumeMembershipDialog = showResumeMembershipDialog,
            isCurrentlyPaused = user.isCurrentlyPaused,
            isEligibleForPause = user.isEligibleForPause,
            isDeveloper = user.isDeveloper ?: false
        )
    }
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(FlowConstants.KEEP_SUBSCRIPTION_ALIVE_DELAY),
            initialValue = MembershipScreenUiState()
        )

    fun onChangeMembershipPlan() {
        navigator.navigate(ChangeMembershipPlanRoute())
    }

    fun onCancelMembership() {
        navigator.navigate(CancelMembershipRoute())
    }

    fun onPauseMembership() {
        navigator.navigate(PauseMembershipRoute())
    }

    fun onResumeMembership() {
        viewModelScope.launch {
            userRepository.resumeMembership()
            userRepository.refreshUser()
            showResumeMembershipDialog(false)
        }
    }

    fun showResumeMembershipDialog(show: Boolean) {
        this.showResumeMembershipDialog = show
    }
}

data class MembershipScreenUiState(
    val appLogoAlpha: Float = 1f,
    val showMembershipCheckmark: Boolean = true,
    val statusTitle: String = "",
    val membershipSinceText: String = "",
    val membershipPlanTitle: String = "",
    val membershipPlanDetails: String = "",
    val showResumeMembershipDialog: Boolean = false,
    val isCurrentlyPaused: Boolean = false,
    val isEligibleForPause: Boolean = false,
    val isDeveloper: Boolean = false
)
