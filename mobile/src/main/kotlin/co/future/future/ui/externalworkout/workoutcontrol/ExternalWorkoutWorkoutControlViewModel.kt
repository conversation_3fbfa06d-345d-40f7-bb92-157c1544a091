package co.future.future.ui.externalworkout.workoutcontrol

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import co.future.future.data.workout.externalworkout.ExternalWorkoutStateMachine
import co.future.future.di.Bridged
import co.future.future.ui.navigator.FutureNavigator
import co.future.future.ui.navigator.GuidedWorkoutMusicServicesRoute
import co.future.futurekit.constants.FlowConstants
import co.future.futurekit.models.ActiveWorkoutState
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.stateIn
import javax.inject.Inject
import kotlin.time.Duration

@HiltViewModel
class ExternalWorkoutWorkoutControlViewModel @Inject constructor(
    private val navigator: FutureNavigator,
    @Bridged private val workoutStateMachine: ExternalWorkoutStateMachine
) : ViewModel(), FutureNavigator by navigator {
    val uiState: StateFlow<ExternalWorkoutWorkoutControlViewUiState> = combine(
        workoutStateMachine.workoutStateFlow,
        workoutStateMachine.workoutProgressFlow
    ) { workoutState, workoutProgress ->
        ExternalWorkoutWorkoutControlViewUiState(
            currentWorkoutState = workoutState.currentWorkoutState,
            hasWorkoutStarted = workoutState.hasWorkoutStarted,
            workoutDuration = workoutState.workout?.duration,
            elapsedDurationForWorkout = workoutProgress.elapsedDurationForWorkout,
            estimatedDurationRemaining = workoutProgress.estimatedDurationRemaining,
        )
    }
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(FlowConstants.KEEP_SUBSCRIPTION_ALIVE_DELAY),
            initialValue = ExternalWorkoutWorkoutControlViewUiState()
        )

    fun pauseWorkout() {
        workoutStateMachine.pauseWorkout()
    }

    fun resumeWorkout() {
        workoutStateMachine.resumeWorkout()
    }

    fun endWorkout() {
        workoutStateMachine.endWorkout()
    }

    fun openMusicServices() {
        navigator.navigate(route = GuidedWorkoutMusicServicesRoute())
    }
}

data class ExternalWorkoutWorkoutControlViewUiState(
    val currentWorkoutState: ActiveWorkoutState = ActiveWorkoutState.UNINITIALIZED,
    val hasWorkoutStarted: Boolean = false,
    val workoutDuration: Duration? = null,
    val elapsedDurationForWorkout: Duration = Duration.ZERO,
    val estimatedDurationRemaining: Duration? = null
)
