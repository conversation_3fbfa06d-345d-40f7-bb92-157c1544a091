package co.future.future.ui.messages

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import co.future.future.extensions.toWholeMinutes
import co.future.futuredesign.FutureTheme
import co.future.futurekit.models.Message
import java.time.format.DateTimeFormatter
import co.future.future.R

@Composable
fun MessageActivitySummaryUpdated(message: Message, isHideCaloriesEnabled: Boolean = false) {
    // if hide calories is enabled,
    // check to make sure we have at least on more health metric to display,
    // hide the summary if no other metrics are available
    if (isHideCaloriesEnabled || message.attributes?.activeEnergyBurned == null && message.attributes?.stepCount == null && message.attributes?.exerciseDuration == null) {
        return
    }

    Box(modifier = Modifier.padding(top = 16.dp, bottom = 16.dp, start = 32.dp, end = 32.dp)) {
        Column(
            modifier = Modifier
                .border(
                    border = BorderStroke(1.dp, FutureTheme.colorScheme.gray80),
                    shape = RoundedCornerShape(20.dp)
                )
                .padding(8.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = message.attributes?.startedAt?.format(DateTimeFormatter.ofPattern("MMM dd, yyyy"))
                    ?: "",
                fontSize = 13.sp,
                color = FutureTheme.colorScheme.textBlack
            )
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = 10.dp, start = 12.dp, end = 12.dp, bottom = 4.dp),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                message.attributes?.stepCount?.let {
                    Column(horizontalAlignment = Alignment.CenterHorizontally) {
                        Text(
                            text = message.attributes?.stepCount.toString(),
                            fontWeight = FontWeight.Medium,
                            fontSize = 17.sp,
                            color = FutureTheme.colorScheme.textBlack
                        )
                        Spacer(modifier = Modifier.size(4.dp))
                        Text(
                            text = stringResource(R.string.messages_activity_summary_steps),
                            fontSize = 13.sp,
                            color = FutureTheme.colorScheme.textBlack
                        )
                    }
                }
                // hide calories is setting is enabled
                if (!isHideCaloriesEnabled) {
                    message.attributes?.activeEnergyBurned?.let {
                        Column(horizontalAlignment = Alignment.CenterHorizontally) {
                            Text(
                                text = message.attributes?.activeEnergyBurned.toString(),
                                fontWeight = FontWeight.Medium,
                                fontSize = 17.sp,
                                color = FutureTheme.colorScheme.textBlack
                            )
                            Spacer(modifier = Modifier.size(4.dp))
                            Text(
                                text = stringResource(R.string.messages_activity_summary_calories),
                                fontSize = 13.sp,
                                color = FutureTheme.colorScheme.textBlack
                            )
                        }
                    }
                }
                message.attributes?.exerciseDuration?.let {
                    Column(horizontalAlignment = Alignment.CenterHorizontally) {
                        Text(
                            text = "${message.attributes?.exerciseDuration?.toWholeMinutes()} min",
                            fontWeight = FontWeight.Medium,
                            fontSize = 17.sp,
                            color = FutureTheme.colorScheme.textBlack
                        )
                        Spacer(modifier = Modifier.size(4.dp))
                        Text(
                            text = stringResource(R.string.messages_activity_summary_active),
                            fontSize = 13.sp,
                            color = FutureTheme.colorScheme.textBlack
                        )
                    }
                }
            }
        }
    }
}
