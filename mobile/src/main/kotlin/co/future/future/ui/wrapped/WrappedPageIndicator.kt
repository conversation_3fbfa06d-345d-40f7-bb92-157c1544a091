package co.future.future.ui.wrapped

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.times
import co.future.future.providers.previewtheme.PreviewThemeProvider
import co.future.future.ui.components.AnimatedLinearProgressIndicator
import co.future.futuredesign.FutureColor
import kotlin.math.max

@Composable
fun WrappedPageIndicator(
    modifier: Modifier = Modifier,
    currentPageIndex: Int = 0,
    totalPageCount: Int = 2,
    activePageTimer: Float = 0f,
    activeColor: Color = FutureColor.Gray70,
    inactiveColor: Color = activeColor.copy(alpha = 0.38f),
    indicatorColor: Color = FutureColor.Black,
    indicatorHeight: Dp = 4.dp,
    spacing: Dp = 4.dp,
    indicatorShape: Shape = CircleShape,
) {
    val configuration = LocalConfiguration.current
    val screenWidth = configuration.screenWidthDp.dp - 36.dp
    val indicatorWidth = (screenWidth - ((totalPageCount - 1) * spacing)) / max(totalPageCount, 1)

    Box(
        modifier = modifier,
        contentAlignment = Alignment.Center
    ) {
        Row(
            horizontalArrangement = Arrangement.spacedBy(spacing),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            repeat(totalPageCount) { index ->
                if (index == currentPageIndex) {
                    Box(
                        modifier = Modifier
                            .size(width = indicatorWidth, height = indicatorHeight)
                            .clip(indicatorShape)
                    ) {
                        AnimatedLinearProgressIndicator(
                            progress = activePageTimer,
                            modifier = Modifier.fillMaxSize(),
                            trackColor = inactiveColor,
                            color = indicatorColor,
                            animationDurationMillis = 1_000
                        )
                    }
                } else {
                    Box(
                        modifier = Modifier
                            .size(width = indicatorWidth, height = indicatorHeight)
                            .clip(indicatorShape)
                            .background(
                                color = if (index < currentPageIndex) activeColor else inactiveColor,
                                shape = indicatorShape
                            )
                    )
                }
            }
        }
    }
}

@Preview(name = "Base")
@Composable
fun WrappedPageIndicator_Preview() {
    PreviewThemeProvider {
        WrappedPageIndicator(
            modifier = Modifier.fillMaxSize(),
            currentPageIndex = 2
        )
    }
}
