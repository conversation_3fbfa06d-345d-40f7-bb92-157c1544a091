package co.future.future.di

import android.content.Context
import androidx.datastore.preferences.core.Preferences
import co.future.future.BuildConfig
import co.future.future.data.userpreferences.UserPreferencesRepository
import co.future.future.data.userpreferences.getServerEnvironment
import co.future.futurekit.utils.Credentials
import co.future.futurekit.models.ServerEnvironment
import com.stripe.android.Stripe
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.runBlocking
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object StripeModule {
    /**
     * Configure stripe with test or live key depending on build config + server environment.
     */
    @Provides
    @Singleton
    fun providesStripe(
        @ApplicationContext applicationContext: Context,
        userPreferencesRepository: UserPreferencesRepository
    ): Stripe {
        val useTestKey = BuildConfig.DEBUG ||
            runBlocking {
                userPreferencesRepository.preferencesFlow.map(Preferences::getServerEnvironment).firstOrNull()
            } != ServerEnvironment.PRODUCTION
        return Stripe(
            context = applicationContext,
            publishableKey = if (useTestKey) Credentials.stripeTestApiKey else Credentials.stripeLiveApiKey
        )
    }
}
