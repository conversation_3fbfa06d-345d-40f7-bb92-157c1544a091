package co.future.future.di

import co.future.future.data.appconfiguration.AppConfigurationRepository
import co.future.future.data.appconfiguration.AppConfigurationRepositoryImpl
import co.future.future.data.billing.BillingRepository
import co.future.future.data.billing.BillingRepositoryImpl
import co.future.future.data.bluetoothle.BluetoothLERepository
import co.future.future.data.bluetoothle.BluetoothLERepositoryImpl
import co.future.future.data.callappointment.CallAppointmentRepository
import co.future.future.data.callappointment.CallAppointmentRepositoryImpl
import co.future.future.data.camera.VideoCaptureRepository
import co.future.future.data.camera.VideoCaptureRepositoryImpl
import co.future.future.data.camera.VideoPlayBackRepository
import co.future.future.data.camera.VideoPlayBackRepositoryImpl
import co.future.future.data.challenge.ChallengeRepository
import co.future.future.data.challenge.ChallengeRepositoryImpl
import co.future.future.data.device.DeviceRepository
import co.future.future.data.device.DeviceRepositoryImpl
import co.future.future.data.equipment.EquipmentRepository
import co.future.future.data.equipment.EquipmentRepositoryImpl
import co.future.future.data.events.DateEventRepositoryImpl
import co.future.future.data.events.DateEventsRepository
import co.future.future.data.events.EventsRepository
import co.future.future.data.events.EventsRepositoryImpl
import co.future.future.data.experiments.ExperimentsRepository
import co.future.future.data.experiments.ExperimentsRepositoryImpl
import co.future.future.data.feature.FeatureRepository
import co.future.future.data.feature.FeatureRepositoryImpl
import co.future.future.data.friends.FriendRepository
import co.future.future.data.friends.FriendRepositoryImpl
import co.future.future.data.healthconnect.HealthConnectRepository
import co.future.future.data.healthconnect.HealthConnectRepositoryImpl
import co.future.future.data.injuries.InjuriesRepository
import co.future.future.data.injuries.InjuriesRepositoryImpl
import co.future.future.data.lead.LeadRepository
import co.future.future.data.lead.LeadRepositoryImpl
import co.future.future.data.location.LocationManager
import co.future.future.data.location.LocationManagerImpl
import co.future.future.data.media.MediaUploadRepository
import co.future.future.data.media.MediaUploadRepositoryImpl
import co.future.future.data.messages.MessagesLocalDataStore
import co.future.future.data.messages.MessagesLocalDataStoreImpl
import co.future.future.data.messages.MessagesRemoteDataSource
import co.future.future.data.messages.MessagesRemoteDataSourceImpl
import co.future.future.data.messages.MessagesRepository
import co.future.future.data.messages.MessagesRepositoryImpl
import co.future.future.data.metricevent.MetricEventRepository
import co.future.future.data.metricevent.MetricEventRepositoryImpl
import co.future.future.data.partnerships.PartnershipsRepository
import co.future.future.data.partnerships.PartnershipsRepositoryImpl
import co.future.future.data.payment.PaymentRepository
import co.future.future.data.payment.PaymentRepositoryImpl
import co.future.future.data.pendingintent.PendingIntentRepository
import co.future.future.data.pendingintent.PendingIntentRepositoryImpl
import co.future.future.data.playstorereviewhelper.PlayStoreReviewHelperRepository
import co.future.future.data.playstorereviewhelper.PlayStoreReviewHelperRepositoryImpl
import co.future.future.data.referral.ReferralRepository
import co.future.future.data.referral.ReferralRepositoryImpl
import co.future.future.data.schedule.ScheduleEditorRepository
import co.future.future.data.schedule.ScheduleEditorRepositoryImpl
import co.future.future.data.servicehealth.ServiceHealthManager
import co.future.future.data.servicehealth.ServiceHealthManagerImpl
import co.future.future.data.trainer.CoachChangeRepository
import co.future.future.data.trainer.CoachChangeRepositoryImpl
import co.future.future.data.trainer.TrainerRepository
import co.future.future.data.trainer.TrainerRepositoryImpl
import co.future.future.data.user.UserRepository
import co.future.future.data.user.UserRepositoryImpl
import co.future.future.data.userpreferences.UserPreferencesRepository
import co.future.future.data.userpreferences.UserPreferencesRepositoryImpl
import co.future.future.data.workout.ExerciseRepository
import co.future.future.data.workout.ExerciseRepositoryImpl
import co.future.future.data.workout.WorkoutPreloadManager
import co.future.future.data.workout.WorkoutPreloadManagerImpl
import co.future.future.data.workout.WorkoutRepository
import co.future.future.data.workout.WorkoutRepositoryImpl
import co.future.future.data.workout.WorkoutSummaryRepository
import co.future.future.data.workout.WorkoutSummaryRepositoryImpl
import co.future.future.data.workout.externalworkout.ExternalWorkoutTemplateRepository
import co.future.future.data.workout.externalworkout.ExternalWorkoutTemplateRepositoryImpl
import co.future.future.data.workout.ongoingworkout.OngoingWorkoutStorageRepository
import co.future.future.data.workout.ongoingworkout.OngoingWorkoutStorageRepositoryImpl
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Suppress("unused")
@Module
@InstallIn(SingletonComponent::class)
internal abstract class DataModule {
    @Binds
    @Singleton
    abstract fun deviceRepository(deviceRepository: DeviceRepositoryImpl): DeviceRepository

    @Binds
    @Singleton
    abstract fun bluetoothLERepository(bluetoothLERepository: BluetoothLERepositoryImpl): BluetoothLERepository

    @Binds
    @Singleton
    abstract fun trainerRepository(trainerRepository: TrainerRepositoryImpl): TrainerRepository

    @Binds
    @Singleton
    abstract fun userPreferencesRepository(
        userPreferencesRepository: UserPreferencesRepositoryImpl
    ): UserPreferencesRepository

    @Binds
    @Singleton
    abstract fun userRepository(userRepository: UserRepositoryImpl): UserRepository

    @Binds
    @Singleton
    abstract fun friendRepository(friendsRepository: FriendRepositoryImpl): FriendRepository

    @Binds
    @Singleton
    abstract fun locationManager(locationManager: LocationManagerImpl): LocationManager

    @Binds
    @Singleton
    abstract fun workoutRepository(workoutRepository: WorkoutRepositoryImpl): WorkoutRepository

    @Binds
    @Singleton
    abstract fun workoutPreloadManager(workoutPreloadManager: WorkoutPreloadManagerImpl): WorkoutPreloadManager

    @Binds
    @Singleton
    abstract fun workoutSummaryRepository(
        workoutSummaryRepository: WorkoutSummaryRepositoryImpl
    ): WorkoutSummaryRepository

    @Binds
    @Singleton
    abstract fun ongoingWorkoutStorageRepository(
        ongoingWorkoutStorageRepository: OngoingWorkoutStorageRepositoryImpl
    ): OngoingWorkoutStorageRepository

    @Binds
    @Singleton
    abstract fun messagingRepository(messagingRepository: MessagesRepositoryImpl): MessagesRepository

    @Binds
    @Singleton
    abstract fun messagesLocalDataStore(messagesLocalDataStore: MessagesLocalDataStoreImpl): MessagesLocalDataStore

    @Binds
    @Singleton
    abstract fun messagesRemoteDataSource(
        messagesRemoteDataSource: MessagesRemoteDataSourceImpl
    ): MessagesRemoteDataSource

    @Binds
    @Singleton
    abstract fun mediaUploadRepository(mediaUploadRepository: MediaUploadRepositoryImpl): MediaUploadRepository

    @Binds
    @Singleton
    abstract fun videoPlayBackRepository(videoPlayBackRepository: VideoPlayBackRepositoryImpl): VideoPlayBackRepository

    @Binds
    @Singleton
    abstract fun videoCaptureRepository(videoCaptureRepository: VideoCaptureRepositoryImpl): VideoCaptureRepository

    @Binds
    @Singleton
    abstract fun callAppointmentRepository(
        callAppointmentRepository: CallAppointmentRepositoryImpl
    ): CallAppointmentRepository

    @Binds
    @Singleton
    abstract fun leadRepository(leadRepository: LeadRepositoryImpl): LeadRepository

    @Binds
    @Singleton
    abstract fun eventsRepository(eventsRepository: EventsRepositoryImpl): EventsRepository

    @Binds
    @Singleton
    abstract fun dateEventRepository(dateEventRepository: DateEventRepositoryImpl): DateEventsRepository

    @Binds
    @Singleton
    abstract fun injuriesRepository(injuriesRepository: InjuriesRepositoryImpl): InjuriesRepository

    @Binds
    @Singleton
    abstract fun metricEventRepository(metricEventRepository: MetricEventRepositoryImpl): MetricEventRepository

    @Binds
    @Singleton
    abstract fun equipmentRepository(equipmentRepository: EquipmentRepositoryImpl): EquipmentRepository

    @Binds
    @Singleton
    abstract fun appConfigurationRepository(
        appConfigurationRepository: AppConfigurationRepositoryImpl
    ): AppConfigurationRepository

    @Binds
    @Singleton
    abstract fun referralRepository(referralRepository: ReferralRepositoryImpl): ReferralRepository

    @Binds
    @Singleton
    abstract fun experimentsRepository(experimentsRepository: ExperimentsRepositoryImpl): ExperimentsRepository

    @Binds
    @Singleton
    abstract fun pendingIntentRepository(pendingIntentRepository: PendingIntentRepositoryImpl): PendingIntentRepository

    @Binds
    @Singleton
    abstract fun paymentRepository(paymentRepository: PaymentRepositoryImpl): PaymentRepository

    @Binds
    @Singleton
    abstract fun challengeRepository(challengeRepository: ChallengeRepositoryImpl): ChallengeRepository

    @Binds
    @Singleton
    abstract fun billingRepository(billingRepository: BillingRepositoryImpl): BillingRepository

    @Binds
    @Singleton
    abstract fun healthConnectRepository(healthConnectRepository: HealthConnectRepositoryImpl): HealthConnectRepository

    @Binds
    @Singleton
    abstract fun playStoreReviewHelperRepository(
        playStoreReviewHelperRepository: PlayStoreReviewHelperRepositoryImpl
    ): PlayStoreReviewHelperRepository

    @Binds
    @Singleton
    abstract fun partnershipsRepository(
        partnershipsRepository: PartnershipsRepositoryImpl
    ): PartnershipsRepository

    @Binds
    @Singleton
    abstract fun scheduleEditorRepository(
        scheduleEditorRepository: ScheduleEditorRepositoryImpl
    ): ScheduleEditorRepository

    @Binds
    @Singleton
    abstract fun exerciseRepository(
        exerciseRepository: ExerciseRepositoryImpl
    ): ExerciseRepository

    @Binds
    @Singleton
    abstract fun featureRepository(
        featureRepository: FeatureRepositoryImpl
    ): FeatureRepository

    @Binds
    @Singleton
    abstract fun serviceHealthManager(
        serviceHealthManager: ServiceHealthManagerImpl
    ): ServiceHealthManager

    @Binds
    @Singleton
    abstract fun coachChangeRepository(
        coachChangeRepository: CoachChangeRepositoryImpl
    ): CoachChangeRepository

    @Binds
    @Singleton
    abstract fun externalWorkoutTemplateRepository(
        externalWorkoutTemplateRepository: ExternalWorkoutTemplateRepositoryImpl
    ): ExternalWorkoutTemplateRepository
}
