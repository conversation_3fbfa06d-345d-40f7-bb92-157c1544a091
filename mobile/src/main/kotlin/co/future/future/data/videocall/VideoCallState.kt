package co.future.future.data.videocall

import co.daily.model.AvailableDevices
import co.daily.model.CallState
import co.daily.settings.FacingModeUpdate

data class VideoCallState(
    val status: CallState,
    val localParticipant: ParticipantDetails,
    val availableDevices: AvailableDevices,
    val activeAudioDeviceId: String?,
    val participantCount: Int,
    val remoteParticipantsToShow: Map<ParticipantDetails.Id.Remote, ParticipantDetails>,
    val activeSpeaker: ParticipantDetails.Id?,
    val cameraDirection: FacingModeUpdate
) {
    companion object {
        fun default(): VideoCallState = VideoCallState(
            status = CallState.initialized,
            localParticipant = ParticipantDetails.defaultLocal,
            availableDevices = AvailableDevices(
                camera = emptyList(),
                microphone = emptyList(),
                speaker = emptyList(),
                audio = emptyList()
            ),
            activeAudioDeviceId = null,
            participantCount = 1,
            remoteParticipantsToShow = emptyMap(),
            activeSpeaker = null,
            cameraDirection = FacingModeUpdate.user
        )
    }
}
