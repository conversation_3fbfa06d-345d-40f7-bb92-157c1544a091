package co.future.future.data.media

import android.content.Context
import android.media.AudioAttributes
import android.media.AudioFocusRequest
import android.media.AudioManager
import android.media.audiofx.Visualizer
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.media3.common.C
import androidx.media3.common.MediaItem
import androidx.media3.common.PlaybackException
import androidx.media3.common.Player
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.exoplayer.source.MediaSource
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.*
import timber.log.Timber
import kotlin.math.absoluteValue

/**
 * [AudioPlayer] is a wrapper around [ExoPlayer] that plays audio media items and enables
 * listeners into player's playback state, media progress, and audio level [AudioPlayerEvent]s
 * (served via Kotlin [SharedFlow]).
 */
class AudioPlayer(context: Context, mediaSourceFactory: MediaSource.Factory) {
    // Underlying ExoPlayer
    private val player = ExoPlayer.Builder(
        context,
    ).setMediaSourceFactory(mediaSourceFactory).build()

    private val mainScope = CoroutineScope(Job() + Dispatchers.Main)

    // Emit audio events
    private val _eventFlow = MutableSharedFlow<AudioPlayerEvent>()
    val eventFlow = _eventFlow.asSharedFlow()

    // Store currently playing media item id
    var currentMediaItemId by mutableStateOf<String?>(null)
        private set

    private var visualizer: Visualizer? = null
    private var playerStatePollingJob: Job? = null
    private var isAudioLevelDataCaptureEnabled = false
    private var isAudioDuckingEnabled = true

    private val audioManager = context.getSystemService(Context.AUDIO_SERVICE) as AudioManager

    private val audioFocusRequest =
        AudioFocusRequest.Builder(AudioManager.AUDIOFOCUS_GAIN_TRANSIENT_MAY_DUCK).run {
            setAudioAttributes(
                AudioAttributes.Builder().run {
                    setContentType(AudioAttributes.CONTENT_TYPE_SPEECH)
                    setUsage(AudioAttributes.USAGE_ASSISTANT)
                }.build()
            )
            setAcceptsDelayedFocusGain(false)
            build()
        }

    // Playback state / events listener
    private val playerListener = object : Player.Listener {
        override fun onPlaybackStateChanged(playbackState: Int) {
            Timber.tag(TAG).i("Playback state changed: ${playbackState.playbackStateDebugString}")

            // Automatically start and stop our polling job based on playback state
            when (playbackState) {
                ExoPlayer.STATE_READY -> launchPlayerStatePollingJob()
                ExoPlayer.STATE_ENDED -> cancelPlayerStatePollingJob()
                else -> {}
            }
        }

        override fun onEvents(player: Player, events: Player.Events) {
            val mediaId = player.currentMediaItem?.mediaId ?: return
            if (events.contains(Player.EVENT_PLAYBACK_STATE_CHANGED)) {
                if (player.playbackState == ExoPlayer.STATE_READY) {
                    // Playback began - the player will be playing since getPlayWhenReady() is true
                    mainScope.launch {
                        _eventFlow.emit(AudioPlayerEvent.PlaybackBegan(mediaId))
                    }
                } else if (player.playbackState == ExoPlayer.STATE_ENDED) {
                    // Relinquish audio focus after a short delay when playback ends
                    if (!events.contains(Player.EVENT_MEDIA_ITEM_TRANSITION)) {
                        val result = audioManager.abandonAudioFocusRequest(audioFocusRequest)
                        Timber.tag(TAG).i("Ended audio focus with result: $result")
                    }

                    val duration = player.duration
                    if (
                        duration != C.TIME_UNSET &&
                        duration > 0 &&
                        player.currentPosition.toFloat().div(duration.toFloat()) >= 1f
                    ) {
                        // ExoPlayer has fully played current media item and ended playback
                        mainScope.launch {
                            _eventFlow.emit(AudioPlayerEvent.PlaybackEnded(mediaId))
                        }
                    }
                }
            }

            // Current media item changed or the player started repeating the current
            // item; store the newly playing media id
            if (events.contains(Player.EVENT_MEDIA_ITEM_TRANSITION)) {
                currentMediaItemId = mediaId
            }
        }

        override fun onPlayerError(error: PlaybackException) {
            Timber.tag(TAG).e("Encountered ExoPlayer error: ${error.errorCodeName}")
        }
    }

    // Data capture listener for [Visualizer] that calculates average audio level from waveform
    private val audioLevelDataCaptureListener = object : Visualizer.OnDataCaptureListener {
        override fun onWaveFormDataCapture(
            visualizer: Visualizer,
            bytes: ByteArray,
            samplingRate: Int
        ) {
            val mediaId = currentMediaItemId ?: return

            // Calculate average from waveform buffer
            if (bytes.isNotEmpty()) {
                val average =
                    bytes.fold(0) { sum, next -> sum + ((next.toInt() and 0xff) - 0x80).absoluteValue } / bytes.size
                mainScope.launch {
                    _eventFlow.emit(AudioPlayerEvent.Level(mediaId = mediaId, value = average))
                }
            }
        }

        override fun onFftDataCapture(visualizer: Visualizer, bytes: ByteArray, samplingRate: Int) {
            // no-op
        }
    }

    init {
        Timber.tag(TAG).i("Initializing audio player")
        player.addListener(playerListener)
    }

    /**
     * Clean up visualizer and [ExoPlayer] instances.
     */
    fun release() {
        Timber.tag(TAG).i("Releasing audio player")

        // Clean up audio level data capture visualizer
        setAudioLevelDataCaptureEnabled(false)

        // Stop and clean up ExoPlayer
        player.removeListener(playerListener)
        player.stop()
        player.release()
    }

    /**
     * Prepare and play [MediaItem].
     */
    fun playMediaItem(mediaItem: MediaItem, replacePlaylist: Boolean = false) {
        requestAudioFocus()

        Timber.tag(TAG).i(
            "Playing media item: ${mediaItem.mediaId} (replace playlist: $replacePlaylist) | " +
                "Playback state: ${player.playbackState.playbackStateDebugString} | " +
                "Current volume: ${player.volume} (player), ${player.deviceVolume} (device) | " +
                "Media items: ${player.mediaItemCount} | " +
                "Errors: ${player.playerError ?: "[none]"}"
        )

        // If playback state is set to [ExoPlayer.STATE_ENDED], we need to coerce the player state
        // back to be [ExoPlayer.STATE_ENDED] before adding media item and prepping the player for
        // playback. We can accomplish this by calling player.stop().
        if (player.playbackState == ExoPlayer.STATE_ENDED) {
            player.stop()
        }

        if (replacePlaylist) {
            player.setMediaItem(mediaItem)
        } else {
            player.addMediaItem(mediaItem)
        }
        player.prepare()
        player.play()
    }

    /**
     * Clear media items in the playlist.
     */
    fun clearMediaItems() {
        Timber.tag(TAG).i("Clearing media items")
        player.clearMediaItems()
    }

    /**
     * Resume playing existing item.
     */
    fun resume() {
        Timber.tag(TAG).i("Resume playing media item")
        player.play()
    }

    /**
     * Pause currently playing item.
     */
    fun pause() {
        Timber.tag(TAG).i("Pausing audio player")
        player.pause()
    }

    /**
     * Stop and clear all media items.
     */
    fun stop() {
        Timber.tag(TAG).i("Stopping audio player and clearing media items")
        player.stop()
        player.clearMediaItems()
    }

    /**
     * Request audio focus
     */
    fun requestAudioFocus() {
        if (!isAudioDuckingEnabled) return

        try {
            val result = audioManager.requestAudioFocus(audioFocusRequest)
            if (result != AudioManager.AUDIOFOCUS_REQUEST_GRANTED) {
                Timber.tag(TAG).e("Failed to gain audio focus with result: $result")
            }
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "Failed to gain audio focus")
        }
    }

    /**
     * Immediately release audio focus.
     */
    fun endAudioFocus() {
        if (!isAudioDuckingEnabled) return

        try {
            val result = audioManager.abandonAudioFocusRequest(audioFocusRequest)
            Timber.tag(TAG).i("Immediately ended audio focus with result: $result")
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "Failed to end audio focus")
        }
    }

    /**
     * Control audio focus / ducking.
     */
    fun setIsAudioDuckingEnabled(enabled: Boolean) {
        isAudioDuckingEnabled = enabled
        if (!enabled) {
            endAudioFocus()
        }
    }

    /**
     * Launches [playerStatePollingJob] coroutine job that inspects current audio player progress and
     * instantiates visualizer for capturing audio level data if needed. Runs every 0.5 seconds.
     */
    private fun launchPlayerStatePollingJob() {
        playerStatePollingJob = flow {
            while (currentCoroutineContext().isActive) {
                // Emit current progress; if current position >= duration, treat the media
                // item as having finished playing and reset its progress back to 0
                val mediaId = currentMediaItemId
                if (mediaId != null && player.duration > 0) {
                    val progress = player.currentPosition.toFloat().div(player.duration.toFloat())
                    emit(Pair(mediaId, if (progress < 1f) progress else 0f))
                }

                // Instantiate visualizer for capturing waveform data if we've already not done
                // so and isAudioLevelDataCaptureEnabled is set to true
                if (
                    isAudioLevelDataCaptureEnabled &&
                    visualizer == null &&
                    player.audioSessionId != C.AUDIO_SESSION_ID_UNSET
                ) {
                    visualizer = Visualizer(player.audioSessionId).apply {
                        measurementMode = Visualizer.MEASUREMENT_MODE_PEAK_RMS
                        captureSize = Visualizer.getCaptureSizeRange()[1]
                        scalingMode = Visualizer.SCALING_MODE_NORMALIZED
                        setDataCaptureListener(
                            audioLevelDataCaptureListener,
                            Visualizer.getMaxCaptureRate(),
                            true,
                            false
                        )
                        val status = setEnabled(true)
                        if (status != Visualizer.SUCCESS) {
                            Timber.tag(TAG).e("Failed to enable visualizer. Status: $status")
                        }
                    }
                }

                // Poll every 0.5 seconds
                delay(PLAYER_STATE_POLLING_RATE_IN_MILLISECONDS)
            }
        }
            .flowOn(Dispatchers.Main) // Must be run on main thread to query player position
            .cancellable()
            .onEach { progress ->
                _eventFlow.emit(
                    AudioPlayerEvent.Progress(
                        mediaId = progress.first,
                        value = progress.second
                    )
                )
            }
            .launchIn(mainScope)
    }

    /**
     * Cancels [playerStatePollingJob] coroutine job.
     */
    private fun cancelPlayerStatePollingJob() {
        playerStatePollingJob?.cancel()
    }

    /**
     * Start or stop capturing audio level data. Note this function does not directly initiate data
     * capture since underlying [ExoPlayer] might not be ready when this function is called.
     * Instead, our playerStatePollingJob will initiate data capture on the next tick.
     */
    fun setAudioLevelDataCaptureEnabled(enabled: Boolean) {
        isAudioLevelDataCaptureEnabled = enabled
        if (!enabled && visualizer != null) {
            visualizer?.release()
            visualizer = null
        }
    }

    /**
     * Stringify [ExoPlayer.getPlaybackState] for debugging.
     */
    private val Int.playbackStateDebugString: String
        get() = when (this) {
            1 -> "IDLE"
            2 -> "BUFFERING"
            3 -> "READY"
            4 -> "ENDED"
            else -> "UNKNOWN"
        }

    companion object {
        private const val TAG = "AudioPlayer"
        const val PLAYER_STATE_POLLING_RATE_IN_MILLISECONDS = 500L
    }
}

sealed class AudioPlayerEvent {
    class PlaybackBegan(val mediaId: String) : AudioPlayerEvent()
    class PlaybackEnded(val mediaId: String) : AudioPlayerEvent()
    class Progress(val mediaId: String, val value: Float) : AudioPlayerEvent()
    class Level(val mediaId: String, val value: Int) : AudioPlayerEvent()
}
