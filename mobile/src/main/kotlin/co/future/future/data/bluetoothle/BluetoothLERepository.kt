package co.future.future.data.bluetoothle

import android.Manifest
import android.bluetooth.BluetoothGattCharacteristic
import android.content.Context
import android.os.Build
import co.future.future.data.userpreferences.UserPreferencesRepository
import co.future.future.data.userpreferences.getLastConnectedBLEDeviceAddress
import co.future.future.extensions.hasPermission
import co.future.future.utils.IoDispatcher
import co.future.futurekit.constants.HealthConstants
import co.future.futurekit.models.HeartRate
import co.future.futurekit.models.HeartRateDeviceType
import co.future.futurekit.utils.BluetoothUtils
import com.benasher44.uuid.uuidFrom
import com.juul.kable.Advertisement
import com.juul.kable.Peripheral
import com.juul.kable.Scanner
import com.juul.kable.State
import com.juul.kable.characteristicOf
import com.juul.kable.peripheral
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.CancellationException
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onCompletion
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import timber.log.Timber
import java.time.ZonedDateTime
import javax.inject.Inject
import javax.inject.Singleton

interface BluetoothLERepository {
    /**
     * [SharedFlow] emitting valid HRM devices.
     */
    val validDeviceSharedFlow: SharedFlow<BluetoothLEDevice>

    /**
     * Current connected & active device.
     */
    val currentActiveDeviceStateFlow: StateFlow<BluetoothLEDevice?>

    /**
     * Convenience accessor to determine if we are connected to a valid ble device.
     */
    val isConnectedToActiveDevice: Boolean

    /**
     * Whether or not we are currently scanning for existing + new bluetooth devices.
     */
    val isScanningStateFlow: StateFlow<Boolean>

    /**
     * [Flow] that emits [HeartRate] data.
     */
    val heartRateFlow: Flow<HeartRate>

    /**
     * Convenience accessor to determine if there was a last connected device.
     */
    val hasLastConnectedDeviceAddress: Boolean

    /**
     * Start scanning for nearby bluetooth le devices.
     */
    fun startScanning(autoConnect: Boolean = true)

    /**
     * Stop scanning for bluetooth devices.
     */
    fun stopScanning()

    /**
     * Connect to a specific bluetooth device for data collection.
     */
    suspend fun connectToDevice(device: BluetoothLEDevice)

    /**
     * Disconnect from specified device. If not specified, disconnect from current active device.
     */
    suspend fun disconnectFromDevice(device: BluetoothLEDevice?)
}

@Singleton
class BluetoothLERepositoryImpl @Inject constructor(
    @ApplicationContext private val applicationContext: Context,
    @IoDispatcher private val ioDispatcher: CoroutineDispatcher,
    private val userPreferencesRepository: UserPreferencesRepository,
) : BluetoothLERepository {
    private val ioScope = CoroutineScope(Job() + ioDispatcher)

    private val _validDeviceSharedFlow = MutableSharedFlow<BluetoothLEDevice>()
    override val validDeviceSharedFlow: SharedFlow<BluetoothLEDevice> =
        _validDeviceSharedFlow.asSharedFlow()

    private val _currentActiveDeviceStateFlow = MutableStateFlow<BluetoothLEDevice?>(null)
    override val currentActiveDeviceStateFlow = _currentActiveDeviceStateFlow.asStateFlow()

    override val isConnectedToActiveDevice: Boolean
        get() = _currentActiveDeviceStateFlow.value != null

    private val _isScanningStateFlow = MutableStateFlow(false)
    override val isScanningStateFlow = _isScanningStateFlow.asStateFlow()

    private val _lastConnectedDeviceAddressFlow = MutableStateFlow<String?>(null)
    override val hasLastConnectedDeviceAddress: Boolean
        get() = _lastConnectedDeviceAddressFlow.value != null

    private val _heartRateFlow = MutableSharedFlow<HeartRate>()
    override val heartRateFlow: Flow<HeartRate> = _heartRateFlow.asSharedFlow()

    private var scanJob: Job? = null
    private val deviceAddressesInFlight =
        java.util.Collections.synchronizedSet(mutableSetOf<String>())
    private val invalidDeviceAddresses =
        java.util.Collections.synchronizedSet(mutableSetOf<String>())

    init {
        userPreferencesRepository.preferencesFlow.onEach {
            _lastConnectedDeviceAddressFlow.emit(it.getLastConnectedBLEDeviceAddress())
        }.launchIn(ioScope)
    }

    override fun startScanning(autoConnect: Boolean) {
        permissionsList.forEach {
            if (!applicationContext.hasPermission(it)) {
                Timber.tag(TAG).w("Bluetooth permission $it is not granted yet. Ignoring...")
                return
            }
        }

        Timber.tag(TAG).i("Start scanning for Bluetooth LE devices")
        scanJob?.cancel()
        scanJob = ioScope.launch {
            Scanner()
                .advertisements
                .filter { advertisement ->
                    val isBlocked = blocklist.find {
                        advertisement.resolvedName?.lowercase()?.startsWith(it) ?: false
                    } != null
                    val isInvalid = invalidDeviceAddresses.contains(advertisement.address)
                    return@filter !isBlocked && !isInvalid
                }
                .catch { cause ->
                    Timber.tag(TAG).e("Error subscribing to advertisements: $cause")
                }
                .onCompletion { cause ->
                    if (cause == null || cause is CancellationException) {
                        Timber.tag(TAG)
                            .d("Subscription cancelled")
                    }
                }
                .collect { advertisement ->
                    if (advertisement.isConnectable == true && advertisement.resolvedName != null) {
                        // Filter out any devices that are still connecting
                        if (deviceAddressesInFlight.contains(advertisement.address)) return@collect
                        deviceAddressesInFlight.add(advertisement.address)

                        ioScope.launch peripheralScope@{
                            val peripheral = <EMAIL>(advertisement)
                            if (peripheral.name == null) {
                                invalidDeviceAddresses.add(advertisement.address)
                                return@peripheralScope
                            }

                            try {
                                Timber.tag(TAG).d(
                                    "Connecting to peripheral ${peripheral.name}"
                                )
                                peripheral.connect()
                            } catch (error: Throwable) {
                                Timber.tag(TAG).e(
                                    "Error connecting to peripheral ${peripheral.name}: ${error.localizedMessage}"
                                )

                                // Remove from connecting device addresses set so we can try again
                                deviceAddressesInFlight.remove(advertisement.address)
                                return@peripheralScope
                            }
                            Timber.tag(TAG)
                                .d("Successfully connected to peripheral ${peripheral.name}!")

                            if (peripheral.hasValidHRM) {
                                Timber.tag(TAG).d(
                                    "${advertisement.resolvedName} is a valid HRM, creating a device profile..."
                                )

                                val device = BluetoothLEDevice(
                                    address = advertisement.address,
                                    name = advertisement.resolvedName ?: peripheral.name
                                        ?: "Unnamed Heart Rate Monitor",
                                    peripheral = peripheral
                                )
                                _validDeviceSharedFlow.emit(device)
                                if (autoConnect && device.address == _lastConnectedDeviceAddressFlow.value) {
                                    try {
                                        Timber.tag(TAG)
                                            .i("Auto-connecting to last connected device")
                                        connectToDevice(device)
                                    } catch (e: Exception) {
                                        Timber.tag(TAG)
                                            .e(e, "Error connecting to last connected device")
                                    }
                                }
                            } else {
                                try {
                                    Timber.tag(TAG)
                                        .d("${advertisement.resolvedName} is invalid, disconnecting...")
                                    invalidDeviceAddresses.add(advertisement.address)
                                    peripheral.disconnect()
                                } catch (error: Throwable) {
                                    Timber.tag(TAG).e(
                                        "Error disconnecting from peripheral ${peripheral.name}: ${error.localizedMessage}"
                                    )
                                }
                            }
                        }
                    }
                }
        }
    }

    override fun stopScanning() {
        Timber.tag(TAG).i("Stop scanning for Bluetooth LE devices")
        scanJob?.cancel()
        deviceAddressesInFlight.clear()
    }

    override suspend fun connectToDevice(device: BluetoothLEDevice) {
        Timber.tag(TAG).i("Connecting to and collecting data from device ${device.name}")

        _currentActiveDeviceStateFlow.emit(device)

        userPreferencesRepository.setLastConnectedBLEDeviceAddress(device.address)

        device.peripheral
            ?.observe(hrmCharacteristic)
            ?.catch { error ->
                Timber.tag(TAG).e("Error collecting heart rate data from $device: $error")
            }
            ?.collect { data ->
                // Process heart rate data
                try {
                    val flag: Int = BluetoothUtils.getIntValue(
                        data,
                        BluetoothGattCharacteristic.FORMAT_UINT8,
                        0
                    ) ?: return@collect
                    val format = if (flag and 0x01 != 0) {
                        BluetoothGattCharacteristic.FORMAT_UINT16
                    } else {
                        BluetoothGattCharacteristic.FORMAT_UINT8
                    }
                    val heartRateValue: Int =
                        BluetoothUtils.getIntValue(data, format, 1) ?: return@collect
                    if (heartRateValue > HealthConstants.MIN_HEART_RATE_VALUE && heartRateValue < HealthConstants.MAX_HEART_RATE_VALUE) {
                        _heartRateFlow.emit(
                            HeartRate(
                                value = heartRateValue,
                                recordedAt = ZonedDateTime.now(),
                                deviceType = HeartRateDeviceType.BLE
                            )
                        )
                    }
                } catch (error: Throwable) {
                    Timber.tag(TAG).e("Error parsing heart rate value: $error")
                }
            }
    }

    override suspend fun disconnectFromDevice(device: BluetoothLEDevice?) {
        val currentDevice = device ?: _currentActiveDeviceStateFlow.value ?: return

        try {
            if (currentDevice.peripheral == null ||
                currentDevice.peripheral.state.value is State.Disconnected ||
                currentDevice.peripheral.state.value is State.Disconnecting
            ) {
                Timber.tag(TAG)
                    .d("Device ${currentDevice.name} is already disconnected or disconnecting")
            } else {
                currentDevice.peripheral.disconnect()
            }
            _currentActiveDeviceStateFlow.emit(null)
            _lastConnectedDeviceAddressFlow.emit(null)
            userPreferencesRepository.setLastConnectedBLEDeviceAddress("")
        } catch (error: Throwable) {
            Timber.tag(TAG)
                .e("Error disconnecting from current active device: ${error.localizedMessage}")
        }
    }

    private val Advertisement.resolvedName: String?
        get() = this.name ?: this.peripheralName

    private val Peripheral.description: String
        get() {
            var result = ""
            this.services?.forEach { service ->
                val characteristics = service.characteristics.joinToString(
                    ", "
                ) { "${it.characteristicUuid} : ${it.properties}" }
                result += "${service.serviceUuid} | ${characteristics}\n"
            }
            return result
        }

    private val Peripheral.hasValidHRM: Boolean
        get() = this.services?.find { service ->
            service.serviceUuid == uuidFrom(HEART_RATE_SERVICE_UUID) &&
                service.characteristics.find { characteristic ->
                    characteristic.characteristicUuid == uuidFrom(HEART_RATE_CHARACTERISTIC_UUID)
                } != null
        } != null

    companion object {
        private const val TAG = "BluetoothLERepository"
        private const val HEART_RATE_SERVICE_UUID = "0000180d-0000-1000-8000-00805f9b34fb"
        private const val HEART_RATE_CHARACTERISTIC_UUID = "00002a37-0000-1000-8000-00805f9b34fb"
        private val hrmCharacteristic = characteristicOf(
            service = HEART_RATE_SERVICE_UUID,
            characteristic = HEART_RATE_CHARACTERISTIC_UUID
        )
        private val blocklist = listOf("chipolo", "ember", "oura", "tile", "[tv]")
        private val permissionsList = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            listOf(
                Manifest.permission.BLUETOOTH_SCAN,
                Manifest.permission.BLUETOOTH_CONNECT
            )
        } else {
            emptyList()
        }
    }
}
