package co.future.future.data.workout.externalworkout

import co.future.futurekit.models.ActiveWorkoutState
import co.future.futurekit.models.Workout

data class ExternalWorkoutState(
    val workout: Workout? = null,
    val currentWorkoutState: ActiveWorkoutState = ActiveWorkoutState.UNINITIALIZED,
    val previousWorkoutState: ActiveWorkoutState = ActiveWorkoutState.UNINITIALIZED,
    val isInitiatingWorkout: Boolean = false
) {
    val effectiveWorkoutState: ActiveWorkoutState
        get() = if (currentWorkoutState == ActiveWorkoutState.PAUSED) previousWorkoutState else currentWorkoutState

    val isWorkoutActive: Boolean
        get() = (currentWorkoutState != ActiveWorkoutState.NOT_STARTED || isInitiatingWorkout) &&
            currentWorkoutState != ActiveWorkoutState.UNINITIALIZED

    val hasWorkoutStarted: Boolean
        get() = currentWorkoutState != ActiveWorkoutState.UNINITIALIZED &&
            currentWorkoutState != ActiveWorkoutState.NOT_STARTED &&
            currentWorkoutState != ActiveWorkoutState.INTRO_AUDIO

    val isPaused: Boolean
        get() = currentWorkoutState == ActiveWorkoutState.PAUSED
}
