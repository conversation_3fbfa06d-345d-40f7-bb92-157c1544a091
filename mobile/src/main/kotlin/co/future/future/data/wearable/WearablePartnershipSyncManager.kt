package co.future.future.data.wearable

import co.future.futurekit.utils.catchWearErrorsSuspend
import co.future.futurekit.constants.WearableConstants.PARTNERSHIP_STATE
import co.future.futurekit.models.WearPartnershipPayload
import co.future.futurekit.serializers.WearPartnershipPayloadSerializer
import co.future.futurekit.serializers.WearPayloadSerializerUtils.toByteArray
import com.google.android.gms.wearable.PutDataMapRequest
import com.google.android.gms.wearable.PutDataRequest
import com.google.android.horologist.annotations.ExperimentalHorologistApi
import com.google.android.horologist.data.WearDataLayerRegistry
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import javax.inject.Inject
import javax.inject.Singleton

interface WearablePartnershipSyncManager {
    fun updatePartnershipState(
        isPartnershipActive: Boolean,
        partnershipGroup: String,
        partnershipCode: String,
        partnershipLeadId: String
    )
}

@OptIn(ExperimentalHorologistApi::class)
@Singleton
class WearablePartnershipSyncManagerImpl @Inject constructor(
    private val registry: WearDataLayerRegistry
) : WearablePartnershipSyncManager {
    val scope = CoroutineScope(Job() + Dispatchers.IO)

    override fun updatePartnershipState(
        isPartnershipActive: Boolean,
        partnershipGroup: String,
        partnershipCode: String,
        partnershipLeadId: String
    ) {
        catchWearErrorsSuspend(scope) {
            val payload = WearPartnershipPayload(
                isPartnershipActive = isPartnershipActive,
                partnershipGroup = partnershipGroup,
                partnershipCode = partnershipCode,
                partnershipLeadId = partnershipLeadId
            ).toByteArray(WearPartnershipPayloadSerializer)

            val request: PutDataRequest = PutDataMapRequest.create(PARTNERSHIP_STATE).run {
                dataMap.putByteArray(PARTNERSHIP_STATE, payload)
                asPutDataRequest()
            }

            registry.dataClient.putDataItem(request)
        }
    }
}
