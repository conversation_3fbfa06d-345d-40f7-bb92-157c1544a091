package co.future.future.data.wearable

import co.future.futurekit.constants.WearableConstants.USER_STATE
import co.future.futurekit.models.User
import co.future.futurekit.models.WearUserPayload
import co.future.futurekit.serializers.WearPayloadSerializerUtils.toByteArray
import co.future.futurekit.serializers.WearUserPayloadSerializer
import co.future.futurekit.utils.catchWearErrorsSuspend
import com.google.android.gms.wearable.PutDataMapRequest
import com.google.android.gms.wearable.PutDataRequest
import com.google.android.horologist.annotations.ExperimentalHorologistApi
import com.google.android.horologist.data.WearDataLayerRegistry
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import javax.inject.Inject
import javax.inject.Singleton

interface WearableUserSyncManager {
    fun updateCurrentUser(
        user: User
    )
}

@OptIn(ExperimentalHorologistApi::class)
@Singleton
class WearableUserSyncManagerImpl @Inject constructor(
    private val registry: WearDataLayerRegistry
) : WearableUserSyncManager {
    val scope = CoroutineScope(Job() + Dispatchers.IO)

    override fun updateCurrentUser(user: User) {
        catchWearErrorsSuspend(scope) {
            val payload = WearUserPayload(
                userId = user.id,
                email = user.email ?: ""
            ).toByteArray(WearUserPayloadSerializer)

            val request: PutDataRequest = PutDataMapRequest.create(USER_STATE).run {
                dataMap.putByteArray(USER_STATE, payload)
                asPutDataRequest()
            }

            registry.dataClient.putDataItem(request)
        }
    }
}
