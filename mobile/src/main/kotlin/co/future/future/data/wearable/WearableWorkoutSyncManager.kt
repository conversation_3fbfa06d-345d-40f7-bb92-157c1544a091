package co.future.future.data.wearable

import co.future.futurekit.constants.WearableConstants.EXTERNAL_WORKOUT_STATE
import co.future.futurekit.constants.WearableConstants.SCHEDULED_WORKOUT_STATE
import co.future.futurekit.constants.WearableConstants.WORKOUT_STATE
import co.future.futurekit.models.ActiveWorkoutState
import co.future.futurekit.models.ExerciseSet
import co.future.futurekit.models.ExerciseSetType
import co.future.futurekit.models.WearExternalWorkoutStatePayload
import co.future.futurekit.models.WearScheduledWorkoutPayload
import co.future.futurekit.models.WearWorkoutStatePayload
import co.future.futurekit.models.Workout
import co.future.futurekit.serializers.WearExternalWorkoutStatePayloadSerializer
import co.future.futurekit.serializers.WearPayloadSerializerUtils.toByteArray
import co.future.futurekit.serializers.WearScheduledWorkoutSerializer
import co.future.futurekit.serializers.WearWorkoutStatePayloadSerializer
import co.future.futurekit.utils.catchWearErrorsSuspend
import com.google.android.gms.wearable.PutDataMapRequest
import com.google.android.gms.wearable.PutDataRequest
import com.google.android.horologist.annotations.ExperimentalHorologistApi
import com.google.android.horologist.data.WearDataLayerRegistry
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import javax.inject.Inject
import javax.inject.Singleton

interface WearableWorkoutSyncManager {
    fun updateWorkoutState(
        workout: Workout,
        newWorkoutState: ActiveWorkoutState = ActiveWorkoutState.UNINITIALIZED,
        previousWorkoutState: ActiveWorkoutState = ActiveWorkoutState.UNINITIALIZED,
        skipIntroAudio: Boolean = false,
        isInitiatingWorkout: Boolean = false,
        currentExerciseSetIndex: Int = 0,
        previousExerciseSetIndex: Int = 0,
        currentDistance: Double = 0.0,
        currentExerciseSet: ExerciseSet?
    )

    fun updateExternalWorkoutState(
        workoutId: String,
        newWorkoutState: ActiveWorkoutState = ActiveWorkoutState.UNINITIALIZED,
        previousWorkoutState: ActiveWorkoutState = ActiveWorkoutState.UNINITIALIZED,
        currentDistance: Double = 0.0
    )

    fun updateScheduledWorkoutState(
        workout: Workout
    )
}

@Singleton
@OptIn(ExperimentalHorologistApi::class)
class WearableWorkoutSyncManagerImpl
@Inject constructor(
    private val registry: WearDataLayerRegistry
) : WearableWorkoutSyncManager {
    val scope = CoroutineScope(Job() + Dispatchers.IO)

    override fun updateWorkoutState(
        workout: Workout,
        newWorkoutState: ActiveWorkoutState,
        previousWorkoutState: ActiveWorkoutState,
        skipIntroAudio: Boolean,
        isInitiatingWorkout: Boolean,
        currentExerciseSetIndex: Int,
        previousExerciseSetIndex: Int,
        currentDistance: Double,
        currentExerciseSet: ExerciseSet?
    ) {
        catchWearErrorsSuspend(scope) {
            val payload = WearWorkoutStatePayload(
                workoutId = workout.id,
                workoutName = workout.name ?: "",
                workoutDescription = workout.description ?: "",
                workoutTimeLeftAfterExerciseSet = workout.timeLeftAfterExerciseSetIndex(currentExerciseSetIndex),
                newWorkoutState = newWorkoutState,
                previousWorkoutState = previousWorkoutState,
                isInitiatingWorkout = isInitiatingWorkout,
                skipIntroAudio = skipIntroAudio,
                currentExerciseSetIndex = currentExerciseSetIndex,
                previousExerciseSetIndex = previousExerciseSetIndex,
                currentDistance = currentDistance,
                exerciseName = currentExerciseSet?.exercise?.name ?: "",
                exerciseId = currentExerciseSet?.exercise?.id ?: "",
                exerciseType = currentExerciseSet?.exercise?.type ?: "",
                exerciseIsWeight = currentExerciseSet?.isWeightBased ?: false,
                exerciseSideDisplayName = currentExerciseSet?.exercise?.side?.shortDisplayName ?: currentExerciseSet?.exercise?.side?.displayName ?: "",
                exerciseSetReps = currentExerciseSet?.reps ?: 0,
                exerciseSetId = currentExerciseSet?.id ?: "",
                exerciseSetType = currentExerciseSet?.type ?: ExerciseSetType.UNKNOWN,
                exerciseSetStartTiming = currentExerciseSet?.startTiming,
                exerciseSetWeight = currentExerciseSet?.weight ?: 0.0,
                exerciseSetDuration = currentExerciseSet?.duration,
                exerciseSetEstimatedDuration = currentExerciseSet?.estimatedDuration
            ).toByteArray(WearWorkoutStatePayloadSerializer)

            val request: PutDataRequest = PutDataMapRequest.create(WORKOUT_STATE).run {
                dataMap.putByteArray(WORKOUT_STATE, payload)
                asPutDataRequest()
            }

            registry.dataClient.putDataItem(request)
        }
    }

    override fun updateExternalWorkoutState(
        workoutId: String,
        newWorkoutState: ActiveWorkoutState,
        previousWorkoutState: ActiveWorkoutState,
        currentDistance: Double
    ) {
        catchWearErrorsSuspend(scope) {
            val payload = WearExternalWorkoutStatePayload(
                workoutId = workoutId,
                newWorkoutState = newWorkoutState,
                previousWorkoutState = previousWorkoutState,
                currentDistance = currentDistance
            ).toByteArray(WearExternalWorkoutStatePayloadSerializer)

            val request: PutDataRequest = PutDataMapRequest.create(EXTERNAL_WORKOUT_STATE).run {
                dataMap.putByteArray(EXTERNAL_WORKOUT_STATE, payload)
                asPutDataRequest()
            }

            registry.dataClient.putDataItem(request)
        }
    }

    override fun updateScheduledWorkoutState(workout: Workout) {
        catchWearErrorsSuspend(scope) {
            val payload = WearScheduledWorkoutPayload(
                workoutId = workout.id,
                workoutName = workout.name ?: "",
                workoutDescription = workout.description ?: "",
                workoutDuration = workout.duration
            ).toByteArray(WearScheduledWorkoutSerializer)

            val request: PutDataRequest = PutDataMapRequest.create(SCHEDULED_WORKOUT_STATE).run {
                dataMap.putByteArray(SCHEDULED_WORKOUT_STATE, payload)
                asPutDataRequest()
            }

            registry.dataClient.putDataItem(request)
        }
    }
}
