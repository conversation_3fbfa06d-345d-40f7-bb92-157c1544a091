package co.future.future.data.friends

import co.future.future.data.user.UserRepository
import co.future.futurekit.extensions.unwrappedData
import co.future.futurekit.api.InviteApi
import co.future.futurekit.api.UserApi
import co.future.futurekit.models.Friend
import co.future.futurekit.models.Invite
import co.future.futurekit.requests.AcceptChallengeRequest
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import timber.log.Timber
import java.time.ZonedDateTime
import javax.inject.Inject

interface FriendRepository {
    val friendsFlow: StateFlow<List<Friend>>
    val receivedInvitesFlow: StateFlow<List<Invite>>
    val sentInvitesFlow: StateFlow<List<Invite>>

    suspend fun refreshFriends()
    suspend fun acceptInvite(invite: Invite)
}

class FriendRepositoryImpl @Inject constructor(
    private val inviteApi: Invite<PERSON>pi,
    private val userApi: User<PERSON><PERSON>,
    private val userRepository: UserRepository
) : FriendRepository {
    private val _friendsFlow = MutableStateFlow<List<Friend>>(emptyList())
    override val friendsFlow: StateFlow<List<Friend>> = _friendsFlow.asStateFlow()

    private val _receivedInvitesFlow = MutableStateFlow<List<Invite>>(emptyList())
    override val receivedInvitesFlow: StateFlow<List<Invite>> = _receivedInvitesFlow.asStateFlow()

    private val _sentInvitesFlow = MutableStateFlow<List<Invite>>(emptyList())
    override val sentInvitesFlow: StateFlow<List<Invite>> = _sentInvitesFlow.asStateFlow()

    override suspend fun refreshFriends() {
        try {
            val user = userRepository.currentUserFlow.value ?: return

            val receivedInvites = userApi.getUserInvites(userId = user.id).unwrappedData()
            _receivedInvitesFlow.emit(receivedInvites)

            val sentInvites = userApi.getUserInvites(userId = user.id, filter = "outgoing").unwrappedData()
            _sentInvitesFlow.emit(sentInvites)

            val friends = userApi.getUserFriends(userId = user.id).unwrappedData()
            _friendsFlow.emit(friends)

            Timber.tag(TAG).i(
                "User friends: ${friends.count()} | User invites: ${receivedInvites.count()} received, ${sentInvites.count()} sent"
            )
        } catch (e: Exception) {
            Timber.tag(TAG).e(e.localizedMessage)
        }
    }

    override suspend fun acceptInvite(invite: Invite) {
        try {
            inviteApi.acceptInvite(invite.id, AcceptChallengeRequest(ZonedDateTime.now()))
            refreshFriends()
        } catch (e: Exception) {
            Timber.tag(TAG).e(e.localizedMessage)
        }
    }

    companion object {
        private const val TAG = "FriendRepository"
    }
}
