package co.future.future.data.challenge

import co.future.future.data.friends.FriendRepository
import co.future.future.data.user.UserRepository
import co.future.futurekit.extensions.unwrappedData
import co.future.futurekit.extensions.unwrappedDataOrNull
import co.future.future.utils.IoDispatcher
import co.future.futurekit.api.ChallengeApi
import co.future.futurekit.api.ParticipantApi
import co.future.futurekit.api.UserApi
import co.future.futurekit.constants.UrlConstants
import co.future.futurekit.models.Challenge
import co.future.futurekit.models.ChallengeType
import co.future.futurekit.models.joinWith
import co.future.futurekit.requests.*
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.*
import timber.log.Timber
import java.time.ZonedDateTime
import javax.inject.Inject
import javax.inject.Singleton

interface ChallengeRepository {
    /**
     * All available challenges.
     */
    val availableChallengesFlow: StateFlow<List<Challenge>?>

    /**
     * Joinable challenges flow.
     */
    val joinableChallengesFlow: StateFlow<List<Challenge>?>

    /**
     * Joined challenges flow.
     */
    val joinedChallengesFlow: StateFlow<List<Challenge>?>

    /**
     * Challenges available to the user, both hidden and visible.
     */
    val userChallengesFlow: StateFlow<List<Challenge>?>

    /**
     * Active challenge flow.
     */
    val activeChallengeFlow: StateFlow<Challenge?>

    /**
     * User's latest unviewed challenge.
     */
    val latestUnviewedChallengeFlow: StateFlow<Challenge?>

    /**
     * Refresh challenges user has joined or is eligible to join.
     */
    suspend fun refreshChallenges()

    /**
     * Get full challenge object based on its id.
     */
    suspend fun getChallenge(challengeId: String): Challenge?

    /**
     * Get the first matching challenge object based on given type.
     */
    suspend fun getChallengeByType(type: ChallengeType): Challenge?

    /**
     * Get challenge invite code for current user for challenge id.
     */
    suspend fun getChallengeInviteUrl(challengeId: String): String?

    /**
     * Join a challenge for current user.  This call will have the user join the challenge using
     * the participant if one already exists, or it will join by creating a new joined participant
     * for the authenticated us.
     */
    suspend fun joinChallenge(challengeId: String): Boolean

    /**
     * Mark challenge's isHidden property to true for current user.
     */
    suspend fun markChallengeAsHidden(challengeId: String)

    /**
     * Mark challenge as viewed for current user.
     */
    suspend fun markChallengeAsViewed(challengeId: String)

    /**
     * Resolve challenge invite code.
     */
    suspend fun resolveChallengeInvite(code: String)
}

@Singleton
class ChallengeRepositoryImpl @Inject constructor(
    @IoDispatcher private val ioDispatcher: CoroutineDispatcher,
    private val challengeApi: ChallengeApi,
    private val participantApi: ParticipantApi,
    private val userApi: UserApi,
    private val userRepository: UserRepository,
    private val friendRepository: FriendRepository,
) : ChallengeRepository {
    private val ioScope = CoroutineScope(Job() + ioDispatcher)

    private val _availableChallengesFlow = MutableStateFlow<List<Challenge>?>(null)
    override val availableChallengesFlow: StateFlow<List<Challenge>?> = _availableChallengesFlow.asStateFlow()

    private val _userChallengesFlow = MutableStateFlow<List<Challenge>?>(null)
    override val userChallengesFlow: StateFlow<List<Challenge>?> = _userChallengesFlow.asStateFlow()

    private val _activeChallengeFlow = MutableStateFlow<Challenge?>(null)
    override val activeChallengeFlow: StateFlow<Challenge?> = _activeChallengeFlow.asStateFlow()

    private val _joinableChallengesFlow = MutableStateFlow<List<Challenge>?>(null)
    override val joinableChallengesFlow: StateFlow<List<Challenge>?> = _joinableChallengesFlow.asStateFlow()

    private val _joinedChallengesFlow = MutableStateFlow<List<Challenge>?>(null)
    override val joinedChallengesFlow: StateFlow<List<Challenge>?> = _joinedChallengesFlow.asStateFlow()

    private val _latestUnviewedChallengeFlow = MutableStateFlow<Challenge?>(null)
    override val latestUnviewedChallengeFlow: StateFlow<Challenge?> =
        _latestUnviewedChallengeFlow.asStateFlow()

    private val currentUserId: String?
        get() = userRepository.currentUserFlow.value?.id

    init {
        userRepository.currentUserFlow
            .onEach { refreshChallenges() }
            .launchIn(ioScope)
    }

    override suspend fun refreshChallenges() {
        try {
            // TODO: Include strong start and ended challenges once we properly support them
            val availableChallenges = challengeApi.getAvailableChallenges()
                .unwrappedData()
                .filter { it.type != ChallengeType.STRONG_START && !INTERNAL_CHALLENGE_IDS.contains(it.id) }
            _availableChallengesFlow.emit(availableChallenges)
            Timber.tag(TAG).i("Fetched ${availableChallenges.count()} available challenges: $availableChallenges")

            val userChallenges = userApi.getUserChallenges(userRepository.currentUserId)
                .unwrappedData()
                .filter { it.type != ChallengeType.STRONG_START && !INTERNAL_CHALLENGE_IDS.contains(it.id) }
            _userChallengesFlow.emit(userChallenges)
            Timber.tag(TAG).i("Fetched ${userChallenges.count()} user challenges: $userChallenges")

            // Check if there's any unviewed challenges
            val latestUnviewedChallenge = userChallenges.firstOrNull {
                val participant = it.challengeParticipantForUserId(currentUserId)
                participant?.completedAt != null && participant.viewedAt == null
            }
            _latestUnviewedChallengeFlow.emit(latestUnviewedChallenge)

            // Check for joined challenges
            val joinedChallenges = userChallenges.filter { it.challengeParticipantForUserId(currentUserId)?.hasJoined ?: false }
            _joinedChallengesFlow.emit(joinedChallenges)

            // Check for joinable challenges from user and available challenges
            val joinedChallengeIds = joinedChallenges.mapNotNull { it.id }.toSet()
            val unjoinedParticipantChallenges = userChallenges.filter {
                !(it.challengeParticipantForUserId(currentUserId)?.hasJoined ?: false)
            }
            val unjoinedAvailableChallenges = availableChallenges.filter {
                !joinedChallengeIds.contains(it.id ?: "")
            }
            val joinableChallenges = unjoinedAvailableChallenges
                .joinWith(unjoinedParticipantChallenges) { it.id }
                .filter {
                    (it.availableAt ?: ZonedDateTime.now()).isBefore(ZonedDateTime.now()) && (it.endsAt ?: ZonedDateTime.now()).isAfter(
                        ZonedDateTime.now()
                    )
                }
            _joinableChallengesFlow.emit(joinableChallenges)

            // Special logic to resolve and fetch full object data for active spring training challenge
            val activeChallenge = userChallenges.firstOrNull {
                it.type == ChallengeType.SPRING_TRAINING_2023 && it.challengeParticipantForUserId(
                    currentUserId
                )?.hasJoined ?: false
            }
            val activeChallengeId = activeChallenge?.id
            if (activeChallengeId != null) {
                challengeApi.getChallenge(activeChallengeId).unwrappedDataOrNull()?.let { fullChallenge ->
                    _activeChallengeFlow.emit(fullChallenge)
                } ?: run {
                    _activeChallengeFlow.emit(activeChallenge)
                }
            }
        } catch (error: Exception) {
            Timber.tag(TAG).e("Error refreshing user challenges. Error: ${error.localizedMessage}")
        }
    }

    override suspend fun getChallenge(challengeId: String): Challenge? {
        return try {
            challengeApi.getChallenge(challengeId).unwrappedData()
        } catch (error: Exception) {
            Timber.tag(TAG).e(
                "Error fetching challenge with id $challengeId. Error: ${error.localizedMessage}"
            )
            null
        }
    }

    override suspend fun getChallengeByType(type: ChallengeType): Challenge? {
        return try {
            // First check user's challenges, then all challenges
            val challengeId =
                availableChallengesFlow.value?.firstOrNull { it.type == type }?.id ?: return null
            getChallenge(challengeId)
        } catch (error: Exception) {
            Timber.tag(TAG)
                .e("Error fetching challenge of type $type. Error: ${error.localizedMessage}")
            null
        }
    }

    override suspend fun getChallengeInviteUrl(challengeId: String): String? {
        val userId = currentUserId ?: return null
        val code = userChallengesFlow.value?.firstOrNull {
            it.id == challengeId
        }?.challengeParticipantForUserId(userId)?.code ?: return null

        return UrlConstants.CHALLENGE_INVITE_BASE_URL + code
    }

    override suspend fun joinChallenge(challengeId: String): Boolean {
        val challenge = getChallenge(challengeId) ?: return false
        val userId = currentUserId ?: return false

        Timber.tag(TAG).i("Joining challenge id $challengeId for user $userId")

        val didJoinSuccessfully: Boolean
        val participant = challenge.challengeParticipantForUserId(userId)
        val participantId = participant?.id
        if (participantId != null) {
            if (participant.hasJoined) {
                Timber.tag(TAG).w("User has already joined this challenge.")
                return true
            }
            didJoinSuccessfully = try {
                val request = AcceptChallengeRequest(acceptedAt = ZonedDateTime.now())
                participantApi.acceptChallenge(participantId = participantId, request = request)
                true
            } catch (error: Exception) {
                Timber.tag(TAG).e(
                    "Error accepting challenge with id ${challenge.id}. Error: ${error.localizedMessage}"
                )
                false
            }
        } else {
            didJoinSuccessfully = try {
                val request = AddChallengeParticipantRequest(
                    userId = userId,
                    acceptedAt = ZonedDateTime.now()
                )
                challengeApi.postChallengeParticipant(challengeId = challengeId, request = request)
                true
            } catch (error: Exception) {
                Timber.tag(TAG).e(
                    "Error posting challenge participant for challenge id $challengeId. Error: ${error.localizedMessage}"
                )
                false
            }
        }

        refreshChallenges()
        return didJoinSuccessfully
    }

    override suspend fun markChallengeAsHidden(challengeId: String) {
        try {
            val challenge = getChallenge(challengeId) ?: return
            val participantId = challenge.challengeParticipantForUserId(currentUserId)?.id ?: return
            participantApi.updateChallengeIsHidden(
                participantId = participantId,
                request = UpdateChallengeHiddenRequest(isChallengeHidden = true)
            )
            refreshChallenges()
        } catch (error: Exception) {
            Timber.tag(TAG).e(
                "Error hiding challenge with id $challengeId. Error: ${error.localizedMessage}"
            )
        }
    }

    override suspend fun markChallengeAsViewed(challengeId: String) {
        try {
            val challenge = getChallenge(challengeId) ?: return
            val participantId = challenge.challengeParticipantForUserId(currentUserId)?.id ?: return
            participantApi.markChallengeAsViewed(
                participantId = participantId,
                request = MarkChallengeAsViewedRequest(viewedAt = ZonedDateTime.now())
            )
            refreshChallenges()
        } catch (error: Exception) {
            Timber.tag(TAG).e(
                "Error marking challenge as viewed with id $challengeId. Error: ${error.localizedMessage}"
            )
        }
    }

    override suspend fun resolveChallengeInvite(code: String) {
        // Handle error at UI level
        val participant = participantApi.resolveInvite(ChallengeInviteResolutionRequest(code = code)).unwrappedData()
        Timber.tag(TAG).i("Successfully resolved challenge invite for code $code: $participant")

        friendRepository.refreshFriends()

        refreshChallenges()
    }

    companion object {
        private const val TAG = "ChallengeRepository"
        private val INTERNAL_CHALLENGE_IDS = listOf(
            "779c2ed6-a00d-44e6-8b21-b1fe83c29a70",
            "fd64499d-2826-4fdd-8c50-8309abd634ae"
        )
    }
}
