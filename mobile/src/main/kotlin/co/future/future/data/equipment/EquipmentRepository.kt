package co.future.future.data.equipment

import co.future.future.data.user.UserRepository
import co.future.futurekit.extensions.unwrappedData
import co.future.futurekit.extensions.unwrappedDataOrNull
import co.future.futurekit.api.EquipmentApi
import co.future.futurekit.models.Equipment
import co.future.futurekit.models.UserLocation
import co.future.futurekit.models.UserLocationSetup
import co.future.futurekit.models.UserLocationType
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

interface EquipmentRepository {
    val availableEquipmentsFlow: StateFlow<List<Equipment>?>
    val userLocationsFlow: StateFlow<List<UserLocation>?>

    fun refreshAvailableEquipments()
    fun refreshUserLocations()
    suspend fun updateUserLocation(type: UserLocationType, setups: List<UserLocationSetup>)
}

@Singleton
class EquipmentRepositoryImpl @Inject constructor(
    private val userRepository: UserRepository,
    private val equipmentApi: EquipmentApi
) : EquipmentRepository {
    private val scope = CoroutineScope(Job() + Dispatchers.IO)

    private val _availableEquipmentsFlow = MutableStateFlow<List<Equipment>?>(null)
    override val availableEquipmentsFlow: StateFlow<List<Equipment>?> = _availableEquipmentsFlow.asStateFlow()

    private val _userLocationsFlow = MutableStateFlow<List<UserLocation>?>(null)
    override val userLocationsFlow: StateFlow<List<UserLocation>?> = _userLocationsFlow.asStateFlow()

    init {
        // Fetch all available equipments
        refreshAvailableEquipments()

        // Update user's home and gym setups when current user changes
        userRepository.currentUserFlow
            .filterNotNull()
            .distinctUntilChanged()
            .onEach { refreshUserLocations() }
            .launchIn(scope)
    }

    override fun refreshAvailableEquipments() {
        scope.launch {
            try {
                val equipments = equipmentApi.getAvailableEquipments().unwrappedData()
                _availableEquipmentsFlow.emit(equipments)
            } catch (e: Exception) {
                Timber.tag(TAG).e(e, "Error fetching available equipments")
                if (_availableEquipmentsFlow.value.isNullOrEmpty()) {
                    _availableEquipmentsFlow.emit(emptyList())
                }
            }
        }
    }

    override fun refreshUserLocations() {
        val userId = userRepository.currentUserFlow.value?.id ?: return
        scope.launch {
            try {
                val userLocations = equipmentApi.getUserLocations(userId)
                    .unwrappedDataOrNull()
                    ?.filter { it.setups?.isNotEmpty() == true }
                    ?.sortedByDescending { it.updatedAt }
                _userLocationsFlow.emit(userLocations)
                Timber.tag(TAG).i("Fetched ${userLocations?.count() ?: "0"} user locations: $userLocations")
            } catch (e: Exception) {
                Timber.tag(TAG).e(e, "Error fetching user locations")
                if (_userLocationsFlow.value.isNullOrEmpty()) {
                    _userLocationsFlow.emit(emptyList())
                }
            }
        }
    }

    override suspend fun updateUserLocation(type: UserLocationType, setups: List<UserLocationSetup>) {
        val userId = userRepository.currentUserFlow.value?.id ?: error("Invalid current user id")
        val userLocation = userLocationsFlow.value
            ?.firstOrNull { it.type == type || it.name?.lowercase() == type.rawValue.lowercase() }
            ?.copy(setups = setups)
            ?: UserLocation(
                name = type.rawValue.replaceFirstChar { it.titlecase() },
                type = type,
                userId = userId,
                setups = setups
            )

        try {
            userLocation.id?.let { userLocationId ->
                val updatedUserLocation = equipmentApi.updateUserLocation(
                    userLocationId = userLocationId,
                    userLocation = userLocation
                ).unwrappedData()
                Timber.tag(TAG).i("Finished updating user location: $updatedUserLocation")
            } ?: run {
                val newUserLocation = equipmentApi.createUserLocation(userLocation).unwrappedData()
                Timber.tag(TAG).i("Finished creating user location: $newUserLocation")
            }
            refreshUserLocations()
        } catch (error: Exception) {
            Timber.tag(TAG).e("Error creating or updating user location: ${error.localizedMessage}")
        }
    }

    companion object {
        private const val TAG = "EquipmentRepository"
    }
}
