package co.future.future.data.wearable

import co.future.futurekit.utils.catchWearErrorsSuspend
import co.future.futurekit.constants.WearableConstants.WORKOUT_SUMMARY
import co.future.futurekit.models.WearWorkoutSummaryStatePayload
import co.future.futurekit.serializers.WearPayloadSerializerUtils.toByteArray
import co.future.futurekit.serializers.WearWorkoutSummaryStatePayloadSerializer
import com.google.android.gms.wearable.PutDataMapRequest
import com.google.android.gms.wearable.PutDataRequest
import com.google.android.horologist.annotations.ExperimentalHorologistApi
import com.google.android.horologist.data.WearDataLayerRegistry
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import javax.inject.Inject
import javax.inject.Singleton

interface WearableWorkoutSummarySyncManager {
    fun updateWorkoutSummary(
        workoutId: String,
        workoutSummaryId: String,
        activeEnergyBurned: Int = 0,
        duration: String,
        distance: Double = 0.0
    )
}

@Singleton
@OptIn(ExperimentalHorologistApi::class)
class WearableWorkoutSummarySyncManagerImpl @Inject constructor(
    private val registry: WearDataLayerRegistry
) : WearableWorkoutSummarySyncManager {
    val scope = CoroutineScope(Job() + Dispatchers.IO)

    override fun updateWorkoutSummary(
        workoutId: String,
        workoutSummaryId: String,
        activeEnergyBurned: Int,
        duration: String,
        distance: Double
    ) {
        catchWearErrorsSuspend(scope) {
            val payload = WearWorkoutSummaryStatePayload(
                workoutId = workoutId,
                workoutSummaryId = workoutSummaryId,
                activeEnergyBurned = activeEnergyBurned,
                duration = duration,
                distance = distance
            ).toByteArray(WearWorkoutSummaryStatePayloadSerializer)

            val request: PutDataRequest =
                PutDataMapRequest.create(WORKOUT_SUMMARY).run {
                    dataMap.putByteArray(WORKOUT_SUMMARY, payload)
                    asPutDataRequest()
                }

            registry.dataClient.putDataItem(request)
        }
    }
}
