package co.future.futurekit.utils

import android.content.Context
import android.content.pm.PackageManager
import android.os.Build
import timber.log.Timber

object PackageInfo {
    var packageName: String = ""
        private set

    var version: String = ""
        private set

    var device: String = ""
        private set

    var sdkLevel: String = ""
        private set

    var architecture: String = ""
        private set

    val packageString: String
        get() {
            return "Package name: $packageName | " +
                "Version: $version | " +
                "Device: $device | " +
                "SDK level: $sdkLevel | " +
                "Architecture: $architecture"
        }

    fun initialize(applicationContext: Context) {
        try {
            val info = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                applicationContext.packageManager.getPackageInfo(
                    applicationContext.packageName,
                    PackageManager.PackageInfoFlags.of(0)
                )
            } else {
                @Suppress("DEPRECATION")
                applicationContext.packageManager.getPackageInfo(applicationContext.packageName, 0)
            }
            packageName = info.packageName
            version = "${info.versionName} (Build ${info.longVersionCode})"
            device = Build.MANUFACTURER + " " + Build.MODEL
            sdkLevel = "${Build.VERSION.SDK_INT}"
            architecture = Build.SUPPORTED_ABIS.firstOrNull() ?: "unknown"
        } catch (error: Throwable) {
            Timber.tag(TAG).e("Error initializing package info: ${error.localizedMessage}")
        }
    }

    private const val TAG = "BuildInfo"
}
