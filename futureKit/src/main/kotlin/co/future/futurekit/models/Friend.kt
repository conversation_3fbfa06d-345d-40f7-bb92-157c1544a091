package co.future.futurekit.models

import co.future.futurekit.serializers.GuaranteedUuidSerializer
import co.future.futurekit.serializers.NullableUuidSerializer
import co.future.futurekit.serializers.ZonedDateTimeSerializer
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import java.time.ZonedDateTime

@Serializable
data class Friend(
    @Serializable(with = GuaranteedUuidSerializer::class)
    @SerialName("id")
    val id: String,

    @Serializable(with = ZonedDateTimeSerializer::class)
    @SerialName("created_at")
    val createdAt: ZonedDateTime? = null,

    @SerialName("user")
    val user: FriendUser
)

@Serializable
data class FriendUser(
    @Serializable(with = GuaranteedUuidSerializer::class)
    @SerialName("id")
    val id: String,

    @SerialName("first_name")
    val firstName: String? = null,

    @SerialName("last_name")
    val lastName: String? = null,

    @SerialName("image_url")
    val imageUrl: String? = null,

    @Serializable(with = NullableUuidSerializer::class)
    @SerialName("current_trainer_id")
    val currentTrainerId: String? = null
) {
    val fullName: String
        get() {
            val nameArray = mutableListOf<String>()
            if (firstName != null && firstName.isNotBlank()) {
                nameArray.add(firstName)
            }
            if (lastName != null && lastName.isNotBlank()) {
                nameArray.add(lastName)
            }
            return if (nameArray.isNotEmpty()) nameArray.joinToString(" ") else ""
        }
}
