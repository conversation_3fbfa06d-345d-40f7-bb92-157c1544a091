package co.future.futurekit.models

import co.future.futurekit.serializers.ZonedDateTimeSerializer
import kotlinx.serialization.Serializable
import java.time.ZonedDateTime

@Serializable
data class WearWorkoutSensorPayload(
    val workoutId: String,
    val heartRate: Double,
    val distance: Double,
    @Serializable(with = ZonedDateTimeSerializer::class)
    val refreshedAt: ZonedDateTime? = ZonedDateTime.now()
)
