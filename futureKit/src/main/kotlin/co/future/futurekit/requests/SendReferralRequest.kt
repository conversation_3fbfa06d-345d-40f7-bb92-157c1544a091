package co.future.futurekit.requests

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class SendReferralRequest(
    @SerialName("to")
    val toName: String,

    @SerialName("to_phone_number")
    val toPhoneNumber: String? = null,

    @SerialName("to_email")
    val toEmail: String? = null,

    @SerialName("referring_user_id")
    val referringUserId: String,

    @SerialName("platform_id")
    val platformId: String,
)
