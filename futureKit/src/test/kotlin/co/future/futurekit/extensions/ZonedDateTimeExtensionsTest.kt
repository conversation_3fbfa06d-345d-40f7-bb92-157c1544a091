package co.future.futurekit.extensions

import com.google.common.truth.Truth
import org.junit.Test
import java.time.DayOfWeek
import java.time.Month
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter

class ZonedDateTimeExtensionsTest {
    private val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss z")

    @Test
    fun `test startOfWeek()`() {
        val dateTime1 = ZonedDateTime.parse("2023-01-09 04:24:21 America/Los_Angeles", formatter)
        val startOfWeek1 = dateTime1.startOfWeek()
        Truth.assertThat(startOfWeek1.dayOfWeek).isEqualTo(DayOfWeek.MONDAY)
        Truth.assertThat(startOfWeek1.month).isEqualTo(Month.JANUARY)
        Truth.assertThat(startOfWeek1.dayOfMonth).isEqualTo(9)

        val dateTime2 = ZonedDateTime.parse("2023-01-10 20:30:45 America/Chicago", formatter)
        val startOfWeek2 = dateTime2.startOfWeek()
        Truth.assertThat(startOfWeek2.dayOfWeek).isEqualTo(DayOfWeek.MONDAY)
        Truth.assertThat(startOfWeek2.month).isEqualTo(Month.JANUARY)
        Truth.assertThat(startOfWeek2.dayOfMonth).isEqualTo(9)

        val dateTime3 = ZonedDateTime.parse("2023-02-01 23:01:08 America/New_York", formatter)
        val startOfWeek3 = dateTime3.startOfWeek()
        Truth.assertThat(startOfWeek3.dayOfWeek).isEqualTo(DayOfWeek.MONDAY)
        Truth.assertThat(startOfWeek3.month).isEqualTo(Month.JANUARY)
        Truth.assertThat(startOfWeek3.dayOfMonth).isEqualTo(30)
    }

    @Test
    fun `test dayDate()`() {
        val dateTime1 = ZonedDateTime.parse("2023-01-09 04:24:21 America/Los_Angeles", formatter)
        val startOfWeek1 = dateTime1.dayDate()
        Truth.assertThat(startOfWeek1.dayOfWeek).isEqualTo(DayOfWeek.MONDAY)
        Truth.assertThat(startOfWeek1.month).isEqualTo(Month.JANUARY)
        Truth.assertThat(startOfWeek1.dayOfMonth).isEqualTo(9)

        val dateTime2 = ZonedDateTime.parse("2023-01-10 20:30:45 America/Chicago", formatter)
        val startOfWeek2 = dateTime2.dayDate()
        Truth.assertThat(startOfWeek2.dayOfWeek).isEqualTo(DayOfWeek.TUESDAY)
        Truth.assertThat(startOfWeek2.month).isEqualTo(Month.JANUARY)
        Truth.assertThat(startOfWeek2.dayOfMonth).isEqualTo(10)

        val dateTime3 = ZonedDateTime.parse("2023-07-01 23:01:08 America/New_York", formatter)
        val startOfWeek3 = dateTime3.dayDate()
        Truth.assertThat(startOfWeek3.dayOfWeek).isEqualTo(DayOfWeek.SATURDAY)
        Truth.assertThat(startOfWeek3.month).isEqualTo(Month.JULY)
        Truth.assertThat(startOfWeek3.dayOfMonth).isEqualTo(1)
    }
}
